//+------------------------------------------------------------------+
//|                                    WaveletSpectralAnalysis.mq4 |
//|                        التحليل الطيفي باستخدام تحويل الموجة      |
//|                     لكشف الترددات المهيمنة والدورات المخفية      |
//+------------------------------------------------------------------+
#property copyright "Wavelet Spectral Analysis"
#property link      ""
#property version   "1.00"
#property strict

// إعدادات المؤشر
#property indicator_separate_window
#property indicator_buffers 6
#property indicator_plots   6

// ألوان المؤشر
#property indicator_color1  clrBlue      // الطيف الرئيسي
#property indicator_color2  clrRed       // التردد المهيمن
#property indicator_color3  clrGreen     // قوة الإشارة
#property indicator_color4  clrOrange    // الطور (Phase)
#property indicator_color5  clrYellow    // إشارة شراء طيفية
#property indicator_color6  clrMagenta   // إشارة بيع طيفية

// أنماط الخطوط
#property indicator_style1  STYLE_SOLID
#property indicator_style2  STYLE_SOLID
#property indicator_style3  STYLE_DASH
#property indicator_style4  STYLE_DOT
#property indicator_style5  STYLE_SOLID
#property indicator_style6  STYLE_SOLID

// عرض الخطوط
#property indicator_width1  2
#property indicator_width2  2
#property indicator_width3  1
#property indicator_width4  1
#property indicator_width5  3
#property indicator_width6  3

//+------------------------------------------------------------------+
//| المدخلات الخارجية                                               |
//+------------------------------------------------------------------+
extern int AnalysisPeriod = 64;          // فترة التحليل الطيفي
extern int MinFrequency = 4;             // أقل تردد للتحليل
extern int MaxFrequency = 32;            // أعلى تردد للتحليل
extern double PowerThreshold = 0.5;      // عتبة قوة الطيف
extern bool DetectDominantFreq = true;   // كشف التردد المهيمن
extern bool ShowPhaseInfo = true;        // إظهار معلومات الطور
extern bool EnableSpectralSignals = true; // تفعيل الإشارات الطيفية
extern double SignalSensitivity = 0.7;   // حساسية الإشارة
extern bool EnableAlerts = false;        // تفعيل التنبيهات
extern string AlertSound = "alert.wav";  // ملف الصوت

//+------------------------------------------------------------------+
//| المتغيرات العامة                                                |
//+------------------------------------------------------------------+
// مصفوفات البيانات
double MainSpectrumBuffer[];      // الطيف الرئيسي
double DominantFreqBuffer[];      // التردد المهيمن
double SignalPowerBuffer[];       // قوة الإشارة
double PhaseBuffer[];             // الطور
double BuySpectralBuffer[];       // إشارات الشراء الطيفية
double SellSpectralBuffer[];      // إشارات البيع الطيفية

// مصفوفات العمل
double TimeSeriesData[];
double FrequencyDomain[];
double PowerSpectrum[];
double PhaseSpectrum[];
double WaveletCoefficients[];

// متغيرات التحليل الطيفي
struct SpectralInfo {
    double dominantFreq;
    double dominantPower;
    double totalPower;
    double spectralCentroid;
    double spectralSpread;
    bool isStable;
};
SpectralInfo CurrentSpectrum;

datetime LastAlertTime = 0;
int FrequencyBins;

//+------------------------------------------------------------------+
//| دالة التهيئة                                                    |
//+------------------------------------------------------------------+
int OnInit()
{
    // ربط المصفوفات
    SetIndexBuffer(0, MainSpectrumBuffer);
    SetIndexBuffer(1, DominantFreqBuffer);
    SetIndexBuffer(2, SignalPowerBuffer);
    SetIndexBuffer(3, PhaseBuffer);
    SetIndexBuffer(4, BuySpectralBuffer);
    SetIndexBuffer(5, SellSpectralBuffer);

    // تسميات المؤشرات
    SetIndexLabel(0, "الطيف الرئيسي");
    SetIndexLabel(1, "التردد المهيمن");
    SetIndexLabel(2, "قوة الإشارة");
    SetIndexLabel(3, "الطور");
    SetIndexLabel(4, "إشارة شراء طيفية");
    SetIndexLabel(5, "إشارة بيع طيفية");

    // أنماط الرسم
    SetIndexStyle(0, DRAW_LINE, STYLE_SOLID, 2, clrBlue);
    SetIndexStyle(1, DRAW_LINE, STYLE_SOLID, 2, clrRed);
    SetIndexStyle(2, DRAW_LINE, STYLE_DASH, 1, clrGreen);
    SetIndexStyle(3, DRAW_LINE, STYLE_DOT, 1, clrOrange);
    SetIndexStyle(4, DRAW_ARROW, STYLE_SOLID, 3, clrYellow);
    SetIndexStyle(5, DRAW_ARROW, STYLE_SOLID, 3, clrMagenta);

    // رموز الأسهم
    SetIndexArrow(4, 233); // سهم شراء
    SetIndexArrow(5, 234); // سهم بيع

    // إخفاء المؤشرات حسب الإعدادات
    if(!ShowPhaseInfo) {
        SetIndexStyle(3, DRAW_NONE);
    }
    if(!EnableSpectralSignals) {
        SetIndexStyle(4, DRAW_NONE);
        SetIndexStyle(5, DRAW_NONE);
    }

    // تهيئة المصفوفات
    FrequencyBins = MaxFrequency - MinFrequency + 1;
    ArrayResize(TimeSeriesData, AnalysisPeriod);
    ArrayResize(FrequencyDomain, AnalysisPeriod);
    ArrayResize(PowerSpectrum, FrequencyBins);
    ArrayResize(PhaseSpectrum, FrequencyBins);
    ArrayResize(WaveletCoefficients, AnalysisPeriod);

    // تهيئة معلومات الطيف
    InitializeSpectralInfo();

    IndicatorShortName("Wavelet Spectral (" + IntegerToString(AnalysisPeriod) + "," + 
                      IntegerToString(MinFrequency) + "-" + IntegerToString(MaxFrequency) + ")");
    
    Print("تم تهيئة التحليل الطيفي بنجاح");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| دالة الحساب الرئيسية                                            |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    if(rates_total < AnalysisPeriod) return 0;
    
    // تحديد نقطة البداية
    int start = MathMax(prev_calculated - 1, AnalysisPeriod - 1);
    if(start < AnalysisPeriod - 1) start = AnalysisPeriod - 1;
    
    // الحساب للشموع المغلقة
    for(int i = start; i >= 1; i--) {
        CalculateSpectralAnalysis(i, close, high, low, rates_total);
    }
    
    return rates_total;
}

//+------------------------------------------------------------------+
//| حساب التحليل الطيفي                                             |
//+------------------------------------------------------------------+
void CalculateSpectralAnalysis(int pos, const double &close[], const double &high[], 
                              const double &low[], int total)
{
    if(pos == 0) return; // تجاهل الشمعة الحالية
    
    // تحضير البيانات الزمنية
    PrepareTimeSeriesData(pos, close, total);
    
    // تطبيق تحويل الموجة للتحليل الطيفي
    ApplyWaveletSpectralTransform();
    
    // حساب الطيف الترددي
    CalculatePowerSpectrum();
    
    // كشف التردد المهيمن
    if(DetectDominantFreq) {
        DetectDominantFrequency();
    }
    
    // حساب الطور
    if(ShowPhaseInfo) {
        CalculatePhaseInformation();
    }
    
    // تحديث المخرجات
    UpdateSpectralOutputs(pos);
    
    // توليد الإشارات الطيفية
    if(EnableSpectralSignals) {
        GenerateSpectralSignals(pos, close, high, low);
    }
}

//+------------------------------------------------------------------+
//| تحضير البيانات الزمنية                                          |
//+------------------------------------------------------------------+
void PrepareTimeSeriesData(int pos, const double &close[], int total)
{
    // نسخ البيانات مع التطبيع
    double sum = 0;
    for(int i = 0; i < AnalysisPeriod; i++) {
        int index = pos - i;
        if(index >= 0 && index < total) {
            TimeSeriesData[i] = close[index];
        } else if(index < 0) {
            TimeSeriesData[i] = close[0];
        } else {
            TimeSeriesData[i] = close[total-1];
        }
        sum += TimeSeriesData[i];
    }
    
    // إزالة المتوسط (Detrending)
    double mean = sum / AnalysisPeriod;
    for(int i = 0; i < AnalysisPeriod; i++) {
        TimeSeriesData[i] -= mean;
    }
    
    // تطبيق نافذة Hanning لتقليل التسرب الطيفي
    ApplyHanningWindow();
}

//+------------------------------------------------------------------+
//| تطبيق نافذة Hanning                                             |
//+------------------------------------------------------------------+
void ApplyHanningWindow()
{
    for(int i = 0; i < AnalysisPeriod; i++) {
        double window = 0.5 * (1 - MathCos(2 * M_PI * i / (AnalysisPeriod - 1)));
        TimeSeriesData[i] *= window;
    }
}

//+------------------------------------------------------------------+
//| تطبيق تحويل الموجة للتحليل الطيفي                               |
//+------------------------------------------------------------------+
void ApplyWaveletSpectralTransform()
{
    // تطبيق تحويل الموجة المستمر (CWT) مبسط
    for(int freq = MinFrequency; freq <= MaxFrequency; freq++) {
        double real = 0, imag = 0;
        
        for(int t = 0; t < AnalysisPeriod; t++) {
            double scale = (double)AnalysisPeriod / freq;
            double normalizedTime = (t - AnalysisPeriod/2) / scale;
            
            // موجة Morlet المعقدة
            double envelope = MathExp(-normalizedTime * normalizedTime / 2);
            double oscillation = MathCos(2 * M_PI * normalizedTime);
            double phase = MathSin(2 * M_PI * normalizedTime);
            
            real += TimeSeriesData[t] * envelope * oscillation;
            imag += TimeSeriesData[t] * envelope * phase;
        }
        
        int freqIndex = freq - MinFrequency;
        if(freqIndex >= 0 && freqIndex < FrequencyBins) {
            PowerSpectrum[freqIndex] = MathSqrt(real*real + imag*imag);
            PhaseSpectrum[freqIndex] = MathArctan2(imag, real);
        }
    }
}

//+------------------------------------------------------------------+
//| حساب الطيف الترددي                                              |
//+------------------------------------------------------------------+
void CalculatePowerSpectrum()
{
    // تطبيع الطيف
    double maxPower = 0;
    for(int i = 0; i < FrequencyBins; i++) {
        maxPower = MathMax(maxPower, PowerSpectrum[i]);
    }
    
    if(maxPower > 0) {
        for(int i = 0; i < FrequencyBins; i++) {
            PowerSpectrum[i] /= maxPower;
        }
    }
    
    // حساب القوة الإجمالية
    CurrentSpectrum.totalPower = 0;
    for(int i = 0; i < FrequencyBins; i++) {
        CurrentSpectrum.totalPower += PowerSpectrum[i];
    }
}

//+------------------------------------------------------------------+
//| كشف التردد المهيمن                                              |
//+------------------------------------------------------------------+
void DetectDominantFrequency()
{
    double maxPower = 0;
    int dominantIndex = 0;
    
    // البحث عن أقوى تردد
    for(int i = 0; i < FrequencyBins; i++) {
        if(PowerSpectrum[i] > maxPower) {
            maxPower = PowerSpectrum[i];
            dominantIndex = i;
        }
    }
    
    CurrentSpectrum.dominantFreq = MinFrequency + dominantIndex;
    CurrentSpectrum.dominantPower = maxPower;
    
    // حساب مركز الطيف (Spectral Centroid)
    double weightedSum = 0, totalWeight = 0;
    for(int i = 0; i < FrequencyBins; i++) {
        double freq = MinFrequency + i;
        weightedSum += freq * PowerSpectrum[i];
        totalWeight += PowerSpectrum[i];
    }
    CurrentSpectrum.spectralCentroid = totalWeight > 0 ? weightedSum / totalWeight : 0;
    
    // حساب انتشار الطيف (Spectral Spread)
    double variance = 0;
    for(int i = 0; i < FrequencyBins; i++) {
        double freq = MinFrequency + i;
        double diff = freq - CurrentSpectrum.spectralCentroid;
        variance += diff * diff * PowerSpectrum[i];
    }
    CurrentSpectrum.spectralSpread = totalWeight > 0 ? MathSqrt(variance / totalWeight) : 0;
}

//+------------------------------------------------------------------+
//| حساب معلومات الطور                                              |
//+------------------------------------------------------------------+
void CalculatePhaseInformation()
{
    // حساب استقرار الطور
    double phaseVariance = 0;
    double meanPhase = 0;
    
    for(int i = 0; i < FrequencyBins; i++) {
        meanPhase += PhaseSpectrum[i];
    }
    meanPhase /= FrequencyBins;
    
    for(int i = 0; i < FrequencyBins; i++) {
        double diff = PhaseSpectrum[i] - meanPhase;
        phaseVariance += diff * diff;
    }
    phaseVariance /= FrequencyBins;
    
    CurrentSpectrum.isStable = phaseVariance < 1.0; // عتبة الاستقرار
}

//+------------------------------------------------------------------+
//| تحديث المخرجات الطيفية                                          |
//+------------------------------------------------------------------+
void UpdateSpectralOutputs(int pos)
{
    // الطيف الرئيسي (متوسط القوة)
    MainSpectrumBuffer[pos] = CurrentSpectrum.totalPower / FrequencyBins;
    
    // التردد المهيمن
    DominantFreqBuffer[pos] = CurrentSpectrum.dominantFreq;
    
    // قوة الإشارة
    SignalPowerBuffer[pos] = CurrentSpectrum.dominantPower;
    
    // الطور (للتردد المهيمن)
    if(ShowPhaseInfo) {
        int dominantIndex = (int)(CurrentSpectrum.dominantFreq - MinFrequency);
        if(dominantIndex >= 0 && dominantIndex < FrequencyBins) {
            PhaseBuffer[pos] = PhaseSpectrum[dominantIndex];
        }
    }
}

//+------------------------------------------------------------------+
//| توليد الإشارات الطيفية                                          |
//+------------------------------------------------------------------+
void GenerateSpectralSignals(int pos, const double &close[], const double &high[], const double &low[])
{
    BuySpectralBuffer[pos] = EMPTY_VALUE;
    SellSpectralBuffer[pos] = EMPTY_VALUE;
    
    if(pos < 3) return;
    
    // شروط إشارة الشراء الطيفية
    bool spectralBuySignal = (CurrentSpectrum.dominantPower > PowerThreshold) &&
                            (CurrentSpectrum.spectralCentroid > (MinFrequency + MaxFrequency) / 2) &&
                            (MainSpectrumBuffer[pos] > MainSpectrumBuffer[pos-1]) &&
                            CurrentSpectrum.isStable;
    
    // شروط إشارة البيع الطيفية
    bool spectralSellSignal = (CurrentSpectrum.dominantPower > PowerThreshold) &&
                             (CurrentSpectrum.spectralCentroid < (MinFrequency + MaxFrequency) / 2) &&
                             (MainSpectrumBuffer[pos] < MainSpectrumBuffer[pos-1]) &&
                             CurrentSpectrum.isStable;
    
    if(spectralBuySignal) {
        BuySpectralBuffer[pos] = low[pos] - 10 * Point;
        if(EnableAlerts) {
            SendSpectralAlert("إشارة شراء طيفية", close[pos], CurrentSpectrum.dominantFreq);
        }
    }
    
    if(spectralSellSignal) {
        SellSpectralBuffer[pos] = high[pos] + 10 * Point;
        if(EnableAlerts) {
            SendSpectralAlert("إشارة بيع طيفية", close[pos], CurrentSpectrum.dominantFreq);
        }
    }
}

//+------------------------------------------------------------------+
//| تهيئة معلومات الطيف                                             |
//+------------------------------------------------------------------+
void InitializeSpectralInfo()
{
    CurrentSpectrum.dominantFreq = 0;
    CurrentSpectrum.dominantPower = 0;
    CurrentSpectrum.totalPower = 0;
    CurrentSpectrum.spectralCentroid = 0;
    CurrentSpectrum.spectralSpread = 0;
    CurrentSpectrum.isStable = false;
}

//+------------------------------------------------------------------+
//| إرسال تنبيه طيفي                                                |
//+------------------------------------------------------------------+
void SendSpectralAlert(string message, double price, double frequency)
{
    datetime currentTime = TimeCurrent();
    if(currentTime - LastAlertTime < 300) return; // تنبيه كل 5 دقائق
    
    string fullMessage = message + " على " + Symbol() +
                        " السعر: " + DoubleToString(price, Digits) +
                        " التردد: " + DoubleToString(frequency, 1);
    
    Alert(fullMessage);
    if(AlertSound != "") {
        PlaySound(AlertSound);
    }
    
    LastAlertTime = currentTime;
}

//+------------------------------------------------------------------+
//| دالة إنهاء التهيئة                                              |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("تم إنهاء التحليل الطيفي");
}
//+------------------------------------------------------------------+
