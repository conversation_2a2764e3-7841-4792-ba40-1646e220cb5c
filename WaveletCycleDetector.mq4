//+------------------------------------------------------------------+
//|                                        WaveletCycleDetector.mq4 |
//|                      كاشف الدورات باستخدام تحليل الموجات        |
//|                          مؤشر مبسط لكشف الأنماط والدورات        |
//+------------------------------------------------------------------+
#property copyright "Wavelet Cycle Detector"
#property link      ""
#property version   "1.50"
#property strict

// إعدادات المؤشر
#property indicator_separate_window
#property indicator_buffers 5
#property indicator_plots   5

// ألوان المؤشر
#property indicator_color1  clrBlue      // الاتجاه الرئيسي
#property indicator_color2  clrRed       // الدورة القصيرة
#property indicator_color3  clrGreen     // الدورة المتوسطة
#property indicator_color4  clrYellow    // إشارة شراء
#property indicator_color5  clrMagenta   // إشارة بيع

// أنماط الخطوط
#property indicator_style1  STYLE_SOLID
#property indicator_style2  STYLE_DASH
#property indicator_style3  STYLE_DASH
#property indicator_style4  STYLE_SOLID
#property indicator_style5  STYLE_SOLID

// عرض الخطوط
#property indicator_width1  2
#property indicator_width2  1
#property indicator_width3  1
#property indicator_width4  3
#property indicator_width5  3

//+------------------------------------------------------------------+
//| المدخلات الخارجية                                               |
//+------------------------------------------------------------------+
extern int MainPeriod = 20;             // الفترة الرئيسية
extern int ShortCyclePeriod = 8;        // فترة الدورة القصيرة
extern int MediumCyclePeriod = 16;      // فترة الدورة المتوسطة
extern double SignalThreshold = 0.3;    // عتبة الإشارة
extern bool ShowSignals = true;         // إظهار الإشارات
extern bool EnableAlerts = false;       // تفعيل التنبيهات
extern string AlertSound = "alert.wav"; // ملف الصوت

//+------------------------------------------------------------------+
//| المتغيرات العامة                                                |
//+------------------------------------------------------------------+
// مصفوفات البيانات
double MainTrendBuffer[];       // الاتجاه الرئيسي
double ShortCycleBuffer[];      // الدورة القصيرة
double MediumCycleBuffer[];     // الدورة المتوسطة
double BuySignalBuffer[];       // إشارات الشراء
double SellSignalBuffer[];      // إشارات البيع

// مصفوفات العمل
double WorkingArray[];
double FilteredData[];

datetime LastAlertTime = 0;

//+------------------------------------------------------------------+
//| دالة التهيئة                                                    |
//+------------------------------------------------------------------+
int OnInit()
{
    // ربط المصفوفات
    SetIndexBuffer(0, MainTrendBuffer);
    SetIndexBuffer(1, ShortCycleBuffer);
    SetIndexBuffer(2, MediumCycleBuffer);
    SetIndexBuffer(3, BuySignalBuffer);
    SetIndexBuffer(4, SellSignalBuffer);

    // تسميات المؤشرات
    SetIndexLabel(0, "الاتجاه الرئيسي");
    SetIndexLabel(1, "الدورة القصيرة");
    SetIndexLabel(2, "الدورة المتوسطة");
    SetIndexLabel(3, "إشارة شراء");
    SetIndexLabel(4, "إشارة بيع");

    // أنماط الرسم
    SetIndexStyle(0, DRAW_LINE, STYLE_SOLID, 2, clrBlue);
    SetIndexStyle(1, DRAW_LINE, STYLE_DASH, 1, clrRed);
    SetIndexStyle(2, DRAW_LINE, STYLE_DASH, 1, clrGreen);
    SetIndexStyle(3, DRAW_ARROW, STYLE_SOLID, 3, clrYellow);
    SetIndexStyle(4, DRAW_ARROW, STYLE_SOLID, 3, clrMagenta);

    // رموز الأسهم
    SetIndexArrow(3, 233); // سهم شراء
    SetIndexArrow(4, 234); // سهم بيع

    // إخفاء الإشارات إذا لم تكن مطلوبة
    if(!ShowSignals) {
        SetIndexStyle(3, DRAW_NONE);
        SetIndexStyle(4, DRAW_NONE);
    }

    // تهيئة المصفوفات
    int maxPeriod = MathMax(MainPeriod, MathMax(ShortCyclePeriod, MediumCyclePeriod));
    ArrayResize(WorkingArray, maxPeriod * 2);
    ArrayResize(FilteredData, maxPeriod * 2);

    IndicatorShortName("Wavelet Cycles (" + IntegerToString(MainPeriod) + "," + 
                      IntegerToString(ShortCyclePeriod) + "," + 
                      IntegerToString(MediumCyclePeriod) + ")");
    
    Print("تم تهيئة كاشف الدورات بنجاح");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| دالة الحساب الرئيسية                                            |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    if(rates_total < MainPeriod) return 0;
    
    // تحديد نقطة البداية
    int start = MathMax(prev_calculated - 1, MainPeriod - 1);
    if(start < MainPeriod - 1) start = MainPeriod - 1;
    
    // الحساب للشموع المغلقة
    for(int i = start; i >= 1; i--) {
        CalculateWaveletCycles(i, close, high, low, rates_total);
    }
    
    return rates_total;
}

//+------------------------------------------------------------------+
//| حساب دورات الموجة                                               |
//+------------------------------------------------------------------+
void CalculateWaveletCycles(int pos, const double &close[], const double &high[], 
                           const double &low[], int total)
{
    if(pos == 0) return; // تجاهل الشمعة الحالية
    
    // تحضير البيانات
    PrepareData(pos, close, total);
    
    // حساب الاتجاه الرئيسي
    MainTrendBuffer[pos] = CalculateMainTrend();
    
    // حساب الدورة القصيرة
    ShortCycleBuffer[pos] = CalculateShortCycle();
    
    // حساب الدورة المتوسطة
    MediumCycleBuffer[pos] = CalculateMediumCycle();
    
    // توليد الإشارات
    if(ShowSignals) {
        GenerateCycleSignals(pos, close, high, low);
    }
}

//+------------------------------------------------------------------+
//| تحضير البيانات                                                  |
//+------------------------------------------------------------------+
void PrepareData(int pos, const double &close[], int total)
{
    // نسخ البيانات
    for(int i = 0; i < MainPeriod; i++) {
        int index = pos - i;
        if(index >= 0 && index < total) {
            WorkingArray[i] = close[index];
        } else if(index < 0) {
            WorkingArray[i] = close[0];
        } else {
            WorkingArray[i] = close[total-1];
        }
    }
    
    // تطبيق مرشح بسيط
    ApplySimpleFilter();
}

//+------------------------------------------------------------------+
//| تطبيق مرشح بسيط                                                 |
//+------------------------------------------------------------------+
void ApplySimpleFilter()
{
    // مرشح متوسط متحرك مرجح
    for(int i = 0; i < MainPeriod; i++) {
        FilteredData[i] = WorkingArray[i];
    }
    
    // تطبيق تنعيم بسيط
    for(int i = 1; i < MainPeriod - 1; i++) {
        FilteredData[i] = (WorkingArray[i-1] + 2*WorkingArray[i] + WorkingArray[i+1]) / 4.0;
    }
}

//+------------------------------------------------------------------+
//| حساب الاتجاه الرئيسي                                            |
//+------------------------------------------------------------------+
double CalculateMainTrend()
{
    double sum = 0;
    int count = 0;
    
    // متوسط مرجح للبيانات المفلترة
    for(int i = 0; i < MainPeriod; i++) {
        double weight = (MainPeriod - i) / (double)MainPeriod;
        sum += FilteredData[i] * weight;
        count++;
    }
    
    return count > 0 ? sum / count : 0;
}

//+------------------------------------------------------------------+
//| حساب الدورة القصيرة                                             |
//+------------------------------------------------------------------+
double CalculateShortCycle()
{
    double sum = 0;
    int count = 0;
    
    // تحليل تردد عالي للدورة القصيرة
    for(int i = 0; i < ShortCyclePeriod && i < MainPeriod; i++) {
        double angle = 2 * MathPi() * i / ShortCyclePeriod;
        sum += FilteredData[i] * MathCos(angle);
        count++;
    }
    
    return count > 0 ? sum / count : 0;
}

//+------------------------------------------------------------------+
//| حساب الدورة المتوسطة                                            |
//+------------------------------------------------------------------+
double CalculateMediumCycle()
{
    double sum = 0;
    int count = 0;
    
    // تحليل تردد متوسط للدورة المتوسطة
    for(int i = 0; i < MediumCyclePeriod && i < MainPeriod; i++) {
        double angle = 2 * MathPi() * i / MediumCyclePeriod;
        sum += FilteredData[i] * MathCos(angle);
        count++;
    }
    
    return count > 0 ? sum / count : 0;
}

//+------------------------------------------------------------------+
//| توليد إشارات الدورة                                             |
//+------------------------------------------------------------------+
void GenerateCycleSignals(int pos, const double &close[], const double &high[], const double &low[])
{
    BuySignalBuffer[pos] = EMPTY_VALUE;
    SellSignalBuffer[pos] = EMPTY_VALUE;
    
    if(pos < 3) return;
    
    // شروط إشارة الشراء
    bool buyCondition = (MainTrendBuffer[pos] > MainTrendBuffer[pos-1]) &&
                       (ShortCycleBuffer[pos] > SignalThreshold) &&
                       (MediumCycleBuffer[pos] > 0) &&
                       (ShortCycleBuffer[pos] > ShortCycleBuffer[pos-1]);
    
    // شروط إشارة البيع
    bool sellCondition = (MainTrendBuffer[pos] < MainTrendBuffer[pos-1]) &&
                        (ShortCycleBuffer[pos] < -SignalThreshold) &&
                        (MediumCycleBuffer[pos] < 0) &&
                        (ShortCycleBuffer[pos] < ShortCycleBuffer[pos-1]);
    
    if(buyCondition) {
        BuySignalBuffer[pos] = low[pos] - 5 * Point;
        if(EnableAlerts) {
            SendCycleAlert("إشارة شراء - دورة الموجة", close[pos]);
        }
    }
    
    if(sellCondition) {
        SellSignalBuffer[pos] = high[pos] + 5 * Point;
        if(EnableAlerts) {
            SendCycleAlert("إشارة بيع - دورة الموجة", close[pos]);
        }
    }
}

//+------------------------------------------------------------------+
//| إرسال تنبيه الدورة                                              |
//+------------------------------------------------------------------+
void SendCycleAlert(string message, double price)
{
    datetime currentTime = TimeCurrent();
    if(currentTime - LastAlertTime < 180) return; // تنبيه كل 3 دقائق
    
    string fullMessage = message + " على " + Symbol() +
                        " السعر: " + DoubleToString(price, Digits);
    
    Alert(fullMessage);
    if(AlertSound != "") {
        PlaySound(AlertSound);
    }
    
    LastAlertTime = currentTime;
}

//+------------------------------------------------------------------+
//| دالة إنهاء التهيئة                                              |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("تم إنهاء كاشف الدورات");
}
//+------------------------------------------------------------------+
