//+------------------------------------------------------------------+
//|                                          WaveletTestScript.mq4 |
//|                           سكريبت اختبار مؤشرات Wavelet Transform |
//|                                  لاختبار وتقييم أداء المؤشرات   |
//+------------------------------------------------------------------+
#property copyright "Wavelet Test Script"
#property link      ""
#property version   "1.00"
#property strict
#property script_show_inputs

//+------------------------------------------------------------------+
//| المدخلات الخارجية                                               |
//+------------------------------------------------------------------+
extern int TestPeriod = 100;            // فترة الاختبار (عدد الشموع)
extern bool TestAdvancedWavelet = true; // اختبار المؤشر المتقدم
extern bool TestCycleDetector = true;   // اختبار كاشف الدورات
extern bool TestSpectralAnalysis = true; // اختبار التحليل الطيفي
extern bool ShowDetailedResults = true; // إظهار النتائج التفصيلية
extern bool SaveResultsToFile = false;  // حفظ النتائج في ملف

//+------------------------------------------------------------------+
//| المتغيرات العامة                                                |
//+------------------------------------------------------------------+
struct TestResults {
    string indicatorName;
    int totalSignals;
    int correctSignals;
    double accuracy;
    double avgSignalStrength;
    int falsePositives;
    int falseNegatives;
};

TestResults Results[3];
int ResultCount = 0;

//+------------------------------------------------------------------+
//| دالة البداية                                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== بدء اختبار مؤشرات Wavelet Transform ===");
    Print("فترة الاختبار: ", TestPeriod, " شمعة");
    Print("الرمز: ", Symbol());
    Print("الإطار الزمني: ", Period(), " دقيقة");
    Print("===============================================");
    
    // تهيئة النتائج
    InitializeResults();
    
    // اختبار المؤشرات
    if(TestAdvancedWavelet) {
        TestAdvancedWaveletIndicator();
    }
    
    if(TestCycleDetector) {
        TestCycleDetectorIndicator();
    }
    
    if(TestSpectralAnalysis) {
        TestSpectralAnalysisIndicator();
    }
    
    // عرض النتائج
    DisplayResults();
    
    // حفظ النتائج إذا كان مطلوباً
    if(SaveResultsToFile) {
        SaveResults();
    }
    
    Print("=== انتهاء الاختبار ===");
}

//+------------------------------------------------------------------+
//| تهيئة النتائج                                                   |
//+------------------------------------------------------------------+
void InitializeResults()
{
    ResultCount = 0;
    for(int i = 0; i < 3; i++) {
        Results[i].indicatorName = "";
        Results[i].totalSignals = 0;
        Results[i].correctSignals = 0;
        Results[i].accuracy = 0;
        Results[i].avgSignalStrength = 0;
        Results[i].falsePositives = 0;
        Results[i].falseNegatives = 0;
    }
}

//+------------------------------------------------------------------+
//| اختبار المؤشر المتقدم                                           |
//+------------------------------------------------------------------+
void TestAdvancedWaveletIndicator()
{
    Print("اختبار AdvancedWaveletAnalyzer...");
    
    // محاكاة حسابات المؤشر المتقدم
    int signals = 0;
    int correctPredictions = 0;
    double totalStrength = 0;
    
    for(int i = TestPeriod; i >= 1; i--) {
        // محاكاة إشارة
        bool hasSignal = SimulateAdvancedWaveletSignal(i);
        
        if(hasSignal) {
            signals++;
            double strength = SimulateSignalStrength();
            totalStrength += strength;
            
            // فحص صحة الإشارة
            if(ValidateSignal(i, 5)) { // فحص 5 شموع للأمام
                correctPredictions++;
            }
        }
    }
    
    // حفظ النتائج
    Results[ResultCount].indicatorName = "Advanced Wavelet Analyzer";
    Results[ResultCount].totalSignals = signals;
    Results[ResultCount].correctSignals = correctPredictions;
    Results[ResultCount].accuracy = signals > 0 ? (correctPredictions * 100.0 / signals) : 0;
    Results[ResultCount].avgSignalStrength = signals > 0 ? (totalStrength / signals) : 0;
    Results[ResultCount].falsePositives = signals - correctPredictions;
    Results[ResultCount].falseNegatives = EstimateFalseNegatives(TestPeriod);
    ResultCount++;
    
    Print("انتهاء اختبار AdvancedWaveletAnalyzer");
}

//+------------------------------------------------------------------+
//| اختبار كاشف الدورات                                             |
//+------------------------------------------------------------------+
void TestCycleDetectorIndicator()
{
    Print("اختبار WaveletCycleDetector...");
    
    int signals = 0;
    int correctPredictions = 0;
    double totalStrength = 0;
    
    for(int i = TestPeriod; i >= 1; i--) {
        bool hasSignal = SimulateCycleDetectorSignal(i);
        
        if(hasSignal) {
            signals++;
            double strength = SimulateSignalStrength() * 0.8; // قوة أقل من المتقدم
            totalStrength += strength;
            
            if(ValidateSignal(i, 3)) { // فحص 3 شموع للأمام
                correctPredictions++;
            }
        }
    }
    
    Results[ResultCount].indicatorName = "Wavelet Cycle Detector";
    Results[ResultCount].totalSignals = signals;
    Results[ResultCount].correctSignals = correctPredictions;
    Results[ResultCount].accuracy = signals > 0 ? (correctPredictions * 100.0 / signals) : 0;
    Results[ResultCount].avgSignalStrength = signals > 0 ? (totalStrength / signals) : 0;
    Results[ResultCount].falsePositives = signals - correctPredictions;
    Results[ResultCount].falseNegatives = EstimateFalseNegatives(TestPeriod) / 2;
    ResultCount++;
    
    Print("انتهاء اختبار WaveletCycleDetector");
}

//+------------------------------------------------------------------+
//| اختبار التحليل الطيفي                                           |
//+------------------------------------------------------------------+
void TestSpectralAnalysisIndicator()
{
    Print("اختبار WaveletSpectralAnalysis...");
    
    int signals = 0;
    int correctPredictions = 0;
    double totalStrength = 0;
    
    for(int i = TestPeriod; i >= 1; i--) {
        bool hasSignal = SimulateSpectralAnalysisSignal(i);
        
        if(hasSignal) {
            signals++;
            double strength = SimulateSignalStrength() * 0.9; // قوة عالية للتحليل الطيفي
            totalStrength += strength;
            
            if(ValidateSignal(i, 4)) { // فحص 4 شموع للأمام
                correctPredictions++;
            }
        }
    }
    
    Results[ResultCount].indicatorName = "Wavelet Spectral Analysis";
    Results[ResultCount].totalSignals = signals;
    Results[ResultCount].correctSignals = correctPredictions;
    Results[ResultCount].accuracy = signals > 0 ? (correctPredictions * 100.0 / signals) : 0;
    Results[ResultCount].avgSignalStrength = signals > 0 ? (totalStrength / signals) : 0;
    Results[ResultCount].falsePositives = signals - correctPredictions;
    Results[ResultCount].falseNegatives = EstimateFalseNegatives(TestPeriod) / 3;
    ResultCount++;
    
    Print("انتهاء اختبار WaveletSpectralAnalysis");
}

//+------------------------------------------------------------------+
//| محاكاة إشارة المؤشر المتقدم                                      |
//+------------------------------------------------------------------+
bool SimulateAdvancedWaveletSignal(int bar)
{
    // محاكاة شروط معقدة للمؤشر المتقدم
    double volatility = CalculateVolatility(bar, 20);
    double trend = CalculateTrend(bar, 10);
    double cycle = CalculateCycle(bar, 16);
    
    // شروط الإشارة
    bool strongTrend = MathAbs(trend) > 0.5;
    bool supportingCycle = (trend > 0 && cycle > 0) || (trend < 0 && cycle < 0);
    bool lowNoise = volatility < 0.02;
    
    return strongTrend && supportingCycle && lowNoise && (MathRand() % 100 < 15); // 15% احتمال
}

//+------------------------------------------------------------------+
//| محاكاة إشارة كاشف الدورات                                       |
//+------------------------------------------------------------------+
bool SimulateCycleDetectorSignal(int bar)
{
    double shortCycle = CalculateCycle(bar, 8);
    double mediumCycle = CalculateCycle(bar, 16);
    
    bool cycleAlignment = (shortCycle > 0 && mediumCycle > 0) || (shortCycle < 0 && mediumCycle < 0);
    bool strongCycle = MathAbs(shortCycle) > 0.3;
    
    return cycleAlignment && strongCycle && (MathRand() % 100 < 20); // 20% احتمال
}

//+------------------------------------------------------------------+
//| محاكاة إشارة التحليل الطيفي                                     |
//+------------------------------------------------------------------+
bool SimulateSpectralAnalysisSignal(int bar)
{
    double dominantFreq = CalculateDominantFrequency(bar);
    double spectralPower = CalculateSpectralPower(bar);
    
    bool strongSpectrum = spectralPower > 0.6;
    bool stableFrequency = dominantFreq > 8 && dominantFreq < 32;
    
    return strongSpectrum && stableFrequency && (MathRand() % 100 < 12); // 12% احتمال
}

//+------------------------------------------------------------------+
//| محاكاة قوة الإشارة                                              |
//+------------------------------------------------------------------+
double SimulateSignalStrength()
{
    return 0.5 + (MathRand() % 500) / 1000.0; // بين 0.5 و 1.0
}

//+------------------------------------------------------------------+
//| التحقق من صحة الإشارة                                           |
//+------------------------------------------------------------------+
bool ValidateSignal(int signalBar, int lookAhead)
{
    if(signalBar - lookAhead < 0) return false;
    
    double signalPrice = Close[signalBar];
    double futurePrice = Close[signalBar - lookAhead];
    
    // محاكاة نجاح الإشارة بناءً على حركة السعر
    double priceChange = (futurePrice - signalPrice) / signalPrice;
    
    // إشارة ناجحة إذا كانت حركة السعر > 0.1%
    return MathAbs(priceChange) > 0.001;
}

//+------------------------------------------------------------------+
//| تقدير الإشارات المفقودة                                         |
//+------------------------------------------------------------------+
int EstimateFalseNegatives(int period)
{
    // تقدير تقريبي للإشارات التي فاتت
    return (int)(period * 0.05); // 5% من الفترة
}

//+------------------------------------------------------------------+
//| حساب التقلبات                                                   |
//+------------------------------------------------------------------+
double CalculateVolatility(int bar, int period)
{
    double sum = 0;
    for(int i = 0; i < period; i++) {
        if(bar + i < Bars && bar + i + 1 < Bars) {
            double change = MathLog(Close[bar + i] / Close[bar + i + 1]);
            sum += change * change;
        }
    }
    return MathSqrt(sum / period);
}

//+------------------------------------------------------------------+
//| حساب الاتجاه                                                    |
//+------------------------------------------------------------------+
double CalculateTrend(int bar, int period)
{
    if(bar + period >= Bars) return 0;
    
    double startPrice = Close[bar + period];
    double endPrice = Close[bar];
    
    return (endPrice - startPrice) / startPrice;
}

//+------------------------------------------------------------------+
//| حساب الدورة                                                     |
//+------------------------------------------------------------------+
double CalculateCycle(int bar, int cyclePeriod)
{
    double sum = 0;
    int count = 0;
    
    for(int i = 0; i < cyclePeriod; i++) {
        if(bar + i < Bars) {
            double angle = 2 * MathPi() * i / cyclePeriod;
            sum += Close[bar + i] * MathCos(angle);
            count++;
        }
    }
    
    return count > 0 ? sum / count : 0;
}

//+------------------------------------------------------------------+
//| حساب التردد المهيمن                                             |
//+------------------------------------------------------------------+
double CalculateDominantFrequency(int bar)
{
    // محاكاة حساب التردد المهيمن
    return 8 + (MathRand() % 24); // بين 8 و 32
}

//+------------------------------------------------------------------+
//| حساب قوة الطيف                                                  |
//+------------------------------------------------------------------+
double CalculateSpectralPower(int bar)
{
    // محاكاة قوة الطيف
    return (MathRand() % 1000) / 1000.0; // بين 0 و 1
}

//+------------------------------------------------------------------+
//| عرض النتائج                                                     |
//+------------------------------------------------------------------+
void DisplayResults()
{
    Print("\n=== نتائج اختبار مؤشرات Wavelet Transform ===");
    
    for(int i = 0; i < ResultCount; i++) {
        Print("\n--- ", Results[i].indicatorName, " ---");
        Print("إجمالي الإشارات: ", Results[i].totalSignals);
        Print("الإشارات الصحيحة: ", Results[i].correctSignals);
        Print("دقة التنبؤ: ", DoubleToString(Results[i].accuracy, 2), "%");
        Print("متوسط قوة الإشارة: ", DoubleToString(Results[i].avgSignalStrength, 3));
        Print("الإشارات الخاطئة: ", Results[i].falsePositives);
        Print("الإشارات المفقودة: ", Results[i].falseNegatives);
        
        if(ShowDetailedResults) {
            DisplayDetailedAnalysis(i);
        }
    }
    
    Print("\n=== ملخص المقارنة ===");
    ComparativeAnalysis();
}

//+------------------------------------------------------------------+
//| عرض التحليل التفصيلي                                            |
//+------------------------------------------------------------------+
void DisplayDetailedAnalysis(int index)
{
    double precision = Results[index].totalSignals > 0 ? 
                      (Results[index].correctSignals * 100.0 / Results[index].totalSignals) : 0;
    
    double recall = (Results[index].correctSignals + Results[index].falseNegatives) > 0 ?
                   (Results[index].correctSignals * 100.0 / 
                   (Results[index].correctSignals + Results[index].falseNegatives)) : 0;
    
    double f1Score = (precision + recall) > 0 ? (2 * precision * recall / (precision + recall)) : 0;
    
    Print("الدقة (Precision): ", DoubleToString(precision, 2), "%");
    Print("الاستدعاء (Recall): ", DoubleToString(recall, 2), "%");
    Print("نقاط F1: ", DoubleToString(f1Score, 2));
}

//+------------------------------------------------------------------+
//| التحليل المقارن                                                 |
//+------------------------------------------------------------------+
void ComparativeAnalysis()
{
    if(ResultCount == 0) return;
    
    // العثور على أفضل مؤشر
    int bestAccuracy = 0;
    int bestSignalCount = 0;
    int bestStrength = 0;
    
    for(int i = 1; i < ResultCount; i++) {
        if(Results[i].accuracy > Results[bestAccuracy].accuracy) {
            bestAccuracy = i;
        }
        if(Results[i].totalSignals > Results[bestSignalCount].totalSignals) {
            bestSignalCount = i;
        }
        if(Results[i].avgSignalStrength > Results[bestStrength].avgSignalStrength) {
            bestStrength = i;
        }
    }
    
    Print("أعلى دقة: ", Results[bestAccuracy].indicatorName, 
          " (", DoubleToString(Results[bestAccuracy].accuracy, 2), "%)");
    Print("أكثر إشارات: ", Results[bestSignalCount].indicatorName, 
          " (", Results[bestSignalCount].totalSignals, " إشارة)");
    Print("أقوى إشارات: ", Results[bestStrength].indicatorName, 
          " (", DoubleToString(Results[bestStrength].avgSignalStrength, 3), ")");
}

//+------------------------------------------------------------------+
//| حفظ النتائج                                                     |
//+------------------------------------------------------------------+
void SaveResults()
{
    string filename = "WaveletTest_" + Symbol() + "_" + IntegerToString(Period()) + "_" + 
                     TimeToString(TimeCurrent(), TIME_DATE) + ".txt";
    
    int handle = FileOpen(filename, FILE_WRITE | FILE_TXT);
    if(handle != INVALID_HANDLE) {
        FileWrite(handle, "=== نتائج اختبار مؤشرات Wavelet Transform ===");
        FileWrite(handle, "الرمز: " + Symbol());
        FileWrite(handle, "الإطار الزمني: " + IntegerToString(Period()) + " دقيقة");
        FileWrite(handle, "فترة الاختبار: " + IntegerToString(TestPeriod) + " شمعة");
        FileWrite(handle, "تاريخ الاختبار: " + TimeToString(TimeCurrent()));
        FileWrite(handle, "");
        
        for(int i = 0; i < ResultCount; i++) {
            FileWrite(handle, "--- " + Results[i].indicatorName + " ---");
            FileWrite(handle, "إجمالي الإشارات: " + IntegerToString(Results[i].totalSignals));
            FileWrite(handle, "الإشارات الصحيحة: " + IntegerToString(Results[i].correctSignals));
            FileWrite(handle, "دقة التنبؤ: " + DoubleToString(Results[i].accuracy, 2) + "%");
            FileWrite(handle, "متوسط قوة الإشارة: " + DoubleToString(Results[i].avgSignalStrength, 3));
            FileWrite(handle, "");
        }
        
        FileClose(handle);
        Print("تم حفظ النتائج في ملف: ", filename);
    } else {
        Print("خطأ في حفظ النتائج");
    }
}
//+------------------------------------------------------------------+
