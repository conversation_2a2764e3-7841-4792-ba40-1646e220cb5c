//+------------------------------------------------------------------+
//|                                            WaveletSettings.mqh |
//|                        إعدادات وثوابت مؤشرات Wavelet Transform |
//|                                   ملف مشترك للإعدادات المحسنة   |
//+------------------------------------------------------------------+
#property copyright "Wavelet Settings Header"
#property link      ""
#property version   "1.00"
#property strict

//+------------------------------------------------------------------+
//| الثوابت الرياضية                                                |
//+------------------------------------------------------------------+
#define PI_2            6.28318530717959    // 2 * PI
#define SQRT_2          1.41421356237310    // الجذر التربيعي لـ 2
#define EULER_GAMMA     0.57721566490153    // ثابت أويلر
#define GOLDEN_RATIO    1.61803398874989    // النسبة الذهبية

//+------------------------------------------------------------------+
//| إعدادات Wavelet Transform                                       |
//+------------------------------------------------------------------+
// أنواع الموجات المدعومة
enum WAVELET_TYPE {
    WAVELET_DAUBECHIES_4,    // موجة Daubechies-4
    WAVELET_DAUBECHIES_6,    // موجة Daubechies-6
    WAVELET_HAAR,            // موجة Haar البسيطة
    WAVELET_BIORTHOGONAL,    // موجة Biorthogonal
    WAVELET_MORLET,          // موجة Morlet للتحليل الطيفي
    WAVELET_MEXICAN_HAT      // موجة Mexican Hat
};

// مستويات التفكيك
enum DECOMPOSITION_LEVEL {
    LEVEL_1 = 1,    // مستوى واحد
    LEVEL_2 = 2,    // مستويان
    LEVEL_3 = 3,    // ثلاثة مستويات
    LEVEL_4 = 4,    // أربعة مستويات
    LEVEL_5 = 5,    // خمسة مستويات
    LEVEL_AUTO = 0  // تحديد تلقائي
};

// أنواع النوافذ للتحليل الطيفي
enum WINDOW_TYPE {
    WINDOW_RECTANGULAR,  // نافذة مستطيلة
    WINDOW_HANNING,      // نافذة Hanning
    WINDOW_HAMMING,      // نافذة Hamming
    WINDOW_BLACKMAN,     // نافذة Blackman
    WINDOW_KAISER        // نافذة Kaiser
};

//+------------------------------------------------------------------+
//| معاملات موجة Daubechies-4                                       |
//+------------------------------------------------------------------+
class CDaubechies4Coefficients {
public:
    static double h0() { return 0.6830127018922193; }
    static double h1() { return 1.1830127018922193; }
    static double h2() { return 0.3169872981077807; }
    static double h3() { return -0.1830127018922193; }
    
    static double g0() { return -h3(); }
    static double g1() { return h2(); }
    static double g2() { return -h1(); }
    static double g3() { return h0(); }
};

//+------------------------------------------------------------------+
//| معاملات موجة Daubechies-6                                       |
//+------------------------------------------------------------------+
class CDaubechies6Coefficients {
public:
    static double h0() { return 0.47046721; }
    static double h1() { return 1.14111692; }
    static double h2() { return 0.650365; }
    static double h3() { return -0.19093442; }
    static double h4() { return -0.12083221; }
    static double h5() { return 0.0498175; }
    
    static double g0() { return -h5(); }
    static double g1() { return h4(); }
    static double g2() { return -h3(); }
    static double g3() { return h2(); }
    static double g4() { return -h1(); }
    static double g5() { return h0(); }
};

//+------------------------------------------------------------------+
//| إعدادات التداول المحسنة                                          |
//+------------------------------------------------------------------+
struct WaveletTradingSettings {
    // إعدادات عامة
    int                 period;              // الفترة الأساسية
    WAVELET_TYPE        waveletType;         // نوع الموجة
    DECOMPOSITION_LEVEL decompositionLevel;  // مستوى التفكيك
    
    // إعدادات الإشارات
    double              signalThreshold;     // عتبة الإشارة
    double              noiseThreshold;      // عتبة الضوضاء
    bool                useAdaptiveThreshold; // استخدام عتبة متكيفة
    
    // إعدادات الدورات
    int                 minCyclePeriod;      // أقل فترة دورة
    int                 maxCyclePeriod;      // أكبر فترة دورة
    bool                detectCycles;        // كشف الدورات
    
    // إعدادات التحليل الطيفي
    WINDOW_TYPE         windowType;          // نوع النافذة
    int                 spectralResolution;  // دقة التحليل الطيفي
    bool                enableSpectralAnalysis; // تفعيل التحليل الطيفي
    
    // إعدادات إدارة المخاطر
    double              maxSignalStrength;   // أقصى قوة إشارة
    int                 signalCooldown;      // فترة انتظار بين الإشارات
    bool                waitForCandleClose;  // انتظار إغلاق الشمعة
    
    // إعدادات التنبيهات
    bool                enableAlerts;        // تفعيل التنبيهات
    string              alertSound;          // ملف الصوت
    int                 alertInterval;       // فترة التنبيه بالثواني
};

//+------------------------------------------------------------------+
//| إعدادات محسنة للأطر الزمنية المختلفة                            |
//+------------------------------------------------------------------+
class CWaveletPresets {
public:
    // إعدادات للتداول السريع (M1, M5)
    static WaveletTradingSettings ScalpingPreset() {
        WaveletTradingSettings settings;
        settings.period = 16;
        settings.waveletType = WAVELET_HAAR;
        settings.decompositionLevel = LEVEL_2;
        settings.signalThreshold = 0.3;
        settings.noiseThreshold = 0.1;
        settings.useAdaptiveThreshold = true;
        settings.minCyclePeriod = 4;
        settings.maxCyclePeriod = 20;
        settings.detectCycles = true;
        settings.windowType = WINDOW_HANNING;
        settings.spectralResolution = 32;
        settings.enableSpectralAnalysis = false;
        settings.maxSignalStrength = 1.0;
        settings.signalCooldown = 60;
        settings.waitForCandleClose = false;
        settings.enableAlerts = true;
        settings.alertSound = "alert.wav";
        settings.alertInterval = 30;
        return settings;
    }
    
    // إعدادات للتداول اليومي (M15, M30, H1)
    static WaveletTradingSettings DayTradingPreset() {
        WaveletTradingSettings settings;
        settings.period = 32;
        settings.waveletType = WAVELET_DAUBECHIES_4;
        settings.decompositionLevel = LEVEL_3;
        settings.signalThreshold = 0.5;
        settings.noiseThreshold = 0.15;
        settings.useAdaptiveThreshold = true;
        settings.minCyclePeriod = 8;
        settings.maxCyclePeriod = 50;
        settings.detectCycles = true;
        settings.windowType = WINDOW_HAMMING;
        settings.spectralResolution = 64;
        settings.enableSpectralAnalysis = true;
        settings.maxSignalStrength = 1.0;
        settings.signalCooldown = 300;
        settings.waitForCandleClose = true;
        settings.enableAlerts = true;
        settings.alertSound = "alert2.wav";
        settings.alertInterval = 180;
        return settings;
    }
    
    // إعدادات للتداول المتوسط (H4, D1)
    static WaveletTradingSettings SwingTradingPreset() {
        WaveletTradingSettings settings;
        settings.period = 64;
        settings.waveletType = WAVELET_DAUBECHIES_6;
        settings.decompositionLevel = LEVEL_4;
        settings.signalThreshold = 0.7;
        settings.noiseThreshold = 0.2;
        settings.useAdaptiveThreshold = false;
        settings.minCyclePeriod = 16;
        settings.maxCyclePeriod = 100;
        settings.detectCycles = true;
        settings.windowType = WINDOW_BLACKMAN;
        settings.spectralResolution = 128;
        settings.enableSpectralAnalysis = true;
        settings.maxSignalStrength = 1.0;
        settings.signalCooldown = 3600;
        settings.waitForCandleClose = true;
        settings.enableAlerts = true;
        settings.alertSound = "alert2.wav";
        settings.alertInterval = 600;
        return settings;
    }
    
    // إعدادات للاستثمار طويل المدى (W1, MN1)
    static WaveletTradingSettings LongTermPreset() {
        WaveletTradingSettings settings;
        settings.period = 128;
        settings.waveletType = WAVELET_BIORTHOGONAL;
        settings.decompositionLevel = LEVEL_5;
        settings.signalThreshold = 0.8;
        settings.noiseThreshold = 0.25;
        settings.useAdaptiveThreshold = false;
        settings.minCyclePeriod = 32;
        settings.maxCyclePeriod = 200;
        settings.detectCycles = true;
        settings.windowType = WINDOW_KAISER;
        settings.spectralResolution = 256;
        settings.enableSpectralAnalysis = true;
        settings.maxSignalStrength = 1.0;
        settings.signalCooldown = 86400;
        settings.waitForCandleClose = true;
        settings.enableAlerts = false;
        settings.alertSound = "";
        settings.alertInterval = 3600;
        return settings;
    }
};

//+------------------------------------------------------------------+
//| إعدادات محسنة لأزواج العملات المختلفة                           |
//+------------------------------------------------------------------+
class CCurrencyPairPresets {
public:
    // إعدادات لأزواج العملات الرئيسية (EUR/USD, GBP/USD, USD/JPY)
    static WaveletTradingSettings MajorPairsPreset() {
        WaveletTradingSettings settings = CWaveletPresets::DayTradingPreset();
        settings.signalThreshold = 0.6;
        settings.noiseThreshold = 0.12;
        settings.minCyclePeriod = 12;
        settings.maxCyclePeriod = 60;
        return settings;
    }
    
    // إعدادات لأزواج العملات الثانوية (EUR/GBP, AUD/USD, USD/CAD)
    static WaveletTradingSettings MinorPairsPreset() {
        WaveletTradingSettings settings = CWaveletPresets::DayTradingPreset();
        settings.signalThreshold = 0.55;
        settings.noiseThreshold = 0.18;
        settings.minCyclePeriod = 10;
        settings.maxCyclePeriod = 45;
        settings.signalCooldown = 450;
        return settings;
    }
    
    // إعدادات لأزواج العملات الغريبة (USD/TRY, EUR/ZAR, GBP/JPY)
    static WaveletTradingSettings ExoticPairsPreset() {
        WaveletTradingSettings settings = CWaveletPresets::DayTradingPreset();
        settings.signalThreshold = 0.75;
        settings.noiseThreshold = 0.25;
        settings.minCyclePeriod = 6;
        settings.maxCyclePeriod = 30;
        settings.useAdaptiveThreshold = true;
        settings.signalCooldown = 600;
        return settings;
    }
    
    // إعدادات للذهب والفضة
    static WaveletTradingSettings PreciousMetalsPreset() {
        WaveletTradingSettings settings = CWaveletPresets::SwingTradingPreset();
        settings.period = 48;
        settings.signalThreshold = 0.65;
        settings.noiseThreshold = 0.2;
        settings.minCyclePeriod = 8;
        settings.maxCyclePeriod = 80;
        return settings;
    }
};

//+------------------------------------------------------------------+
//| دوال مساعدة للحسابات الرياضية                                   |
//+------------------------------------------------------------------+
class CWaveletMath {
public:
    // حساب نافذة Hanning
    static double HanningWindow(int n, int N) {
        return 0.5 * (1.0 - MathCos(2 * MathPi() * n / (N - 1)));
    }
    
    // حساب نافذة Hamming
    static double HammingWindow(int n, int N) {
        return 0.54 - 0.46 * MathCos(2 * MathPi() * n / (N - 1));
    }
    
    // حساب نافذة Blackman
    static double BlackmanWindow(int n, int N) {
        double a0 = 0.42;
        double a1 = 0.5;
        double a2 = 0.08;
        return a0 - a1 * MathCos(2 * MathPi() * n / (N - 1)) + a2 * MathCos(4 * MathPi() * n / (N - 1));
    }
    
    // حساب موجة Morlet
    static double MorletWavelet(double t, double sigma) {
        double envelope = MathExp(-t * t / (2 * sigma * sigma));
        double oscillation = MathCos(5 * t);
        return envelope * oscillation;
    }
    
    // حساب موجة Mexican Hat
    static double MexicanHatWavelet(double t, double sigma) {
        double t_norm = t / sigma;
        double envelope = MathExp(-t_norm * t_norm / 2);
        double polynomial = (1 - t_norm * t_norm);
        return (2 / (MathSqrt(3 * sigma) * MathPow(MathPi(), 0.25))) * polynomial * envelope;
    }
    
    // تطبيع البيانات
    static void NormalizeArray(double &array[], int size) {
        double sum = 0;
        for(int i = 0; i < size; i++) {
            sum += array[i];
        }
        double mean = sum / size;
        
        double variance = 0;
        for(int i = 0; i < size; i++) {
            variance += (array[i] - mean) * (array[i] - mean);
        }
        double stddev = MathSqrt(variance / size);
        
        if(stddev > 0) {
            for(int i = 0; i < size; i++) {
                array[i] = (array[i] - mean) / stddev;
            }
        }
    }
    
    // حساب الارتباط الذاتي
    static double AutoCorrelation(const double &data[], int size, int lag) {
        if(lag >= size) return 0;
        
        double sum1 = 0, sum2 = 0, sum_product = 0;
        int count = size - lag;
        
        for(int i = 0; i < count; i++) {
            sum1 += data[i];
            sum2 += data[i + lag];
            sum_product += data[i] * data[i + lag];
        }
        
        double mean1 = sum1 / count;
        double mean2 = sum2 / count;
        
        double variance1 = 0, variance2 = 0;
        for(int i = 0; i < count; i++) {
            variance1 += (data[i] - mean1) * (data[i] - mean1);
            variance2 += (data[i + lag] - mean2) * (data[i + lag] - mean2);
        }
        
        double stddev1 = MathSqrt(variance1 / count);
        double stddev2 = MathSqrt(variance2 / count);
        
        if(stddev1 > 0 && stddev2 > 0) {
            return (sum_product / count - mean1 * mean2) / (stddev1 * stddev2);
        }
        
        return 0;
    }
};

//+------------------------------------------------------------------+
//| دوال التحقق من صحة الإعدادات                                    |
//+------------------------------------------------------------------+
class CWaveletValidator {
public:
    // التحقق من صحة الإعدادات
    static bool ValidateSettings(const WaveletTradingSettings &settings) {
        if(settings.period < 4 || settings.period > 512) {
            Print("خطأ: الفترة يجب أن تكون بين 4 و 512");
            return false;
        }
        
        if(settings.signalThreshold < 0 || settings.signalThreshold > 1) {
            Print("خطأ: عتبة الإشارة يجب أن تكون بين 0 و 1");
            return false;
        }
        
        if(settings.minCyclePeriod >= settings.maxCyclePeriod) {
            Print("خطأ: أقل فترة دورة يجب أن تكون أصغر من أكبر فترة دورة");
            return false;
        }
        
        if(settings.signalCooldown < 0) {
            Print("خطأ: فترة انتظار الإشارة يجب أن تكون موجبة");
            return false;
        }
        
        return true;
    }
    
    // اقتراح إعدادات محسنة حسب الإطار الزمني
    static WaveletTradingSettings SuggestSettings(int timeframe) {
        switch(timeframe) {
            case PERIOD_M1:
            case PERIOD_M5:
                return CWaveletPresets::ScalpingPreset();
                
            case PERIOD_M15:
            case PERIOD_M30:
            case PERIOD_H1:
                return CWaveletPresets::DayTradingPreset();
                
            case PERIOD_H4:
            case PERIOD_D1:
                return CWaveletPresets::SwingTradingPreset();
                
            case PERIOD_W1:
            case PERIOD_MN1:
                return CWaveletPresets::LongTermPreset();
                
            default:
                return CWaveletPresets::DayTradingPreset();
        }
    }
};

//+------------------------------------------------------------------+
