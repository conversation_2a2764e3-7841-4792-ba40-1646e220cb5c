//+------------------------------------------------------------------+
//|                                                SimpleWavelet.mq4 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict
#property indicator_separate_window
#property indicator_buffers 4

//--- input parameters
input int      WaveletPeriod = 20;
input double   SignalThreshold = 0.5;
input bool     ShowSignals = true;

//--- indicator buffers
double TrendBuffer[];
double DetailBuffer[];
double BuySignalBuffer[];
double SellSignalBuffer[];

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
    SetIndexBuffer(0, TrendBuffer);
    SetIndexBuffer(1, DetailBuffer);
    SetIndexBuffer(2, BuySignalBuffer);
    SetIndexBuffer(3, SellSignalBuffer);

    ArraySetAsSeries(TrendBuffer, true);
    ArraySetAsSeries(DetailBuffer, true);
    ArraySetAsSeries(BuySignalBuffer, true);
    ArraySetAsSeries(SellSignalBuffer, true);

    SetIndexStyle(0, DRAW_LINE, STYLE_SOLID, 2, clrBlue);
    SetIndexStyle(1, DRAW_LINE, STYLE_SOLID, 1, clrRed);
    SetIndexStyle(2, ShowSignals ? DRAW_ARROW : DRAW_NONE, STYLE_SOLID, 3, clrLime);
    SetIndexStyle(3, ShowSignals ? DRAW_ARROW : DRAW_NONE, STYLE_SOLID, 3, clrRed);

    SetIndexArrow(2, 233);
    SetIndexArrow(3, 234);

    SetIndexLabel(0, "Trend");
    SetIndexLabel(1, "Detail");
    SetIndexLabel(2, "Buy Signal");
    SetIndexLabel(3, "Sell Signal");

    SetIndexEmptyValue(0, EMPTY_VALUE);
    SetIndexEmptyValue(1, EMPTY_VALUE);
    SetIndexEmptyValue(2, EMPTY_VALUE);
    SetIndexEmptyValue(3, EMPTY_VALUE);

    IndicatorShortName("Simple Wavelet (" + IntegerToString(WaveletPeriod) + ")");
    Print("Simple Wavelet Indicator initialized successfully");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    if(rates_total < WaveletPeriod) return(0);

    // تحديد نقطة البداية للحساب
    int limit = rates_total - prev_calculated;
    if(prev_calculated == 0) {
        limit = rates_total - WaveletPeriod;
    }

    // حساب المؤشر من الشمعة الأقدم إلى الحالية
    for(int i = limit; i >= 0; i--)
    {
        CalculateWavelet(i, close, rates_total);
    }

    return(rates_total);
}

//+------------------------------------------------------------------+
//| Calculate Wavelet Transform                                      |
//+------------------------------------------------------------------+
void CalculateWavelet(int pos, const double &price[], int rates_total)
{
    // التحقق من وجود بيانات كافية
    if(rates_total - pos < WaveletPeriod) {
        TrendBuffer[pos] = EMPTY_VALUE;
        DetailBuffer[pos] = EMPTY_VALUE;
        BuySignalBuffer[pos] = EMPTY_VALUE;
        SellSignalBuffer[pos] = EMPTY_VALUE;
        return;
    }

    // حساب المتوسط المتحرك (الاتجاه)
    double sum = 0;
    for(int i = 0; i < WaveletPeriod; i++) {
        sum += price[pos + i];
    }
    double trend = sum / WaveletPeriod;
    TrendBuffer[pos] = trend;

    // حساب التفاصيل (الانحراف عن الاتجاه)
    double detail = price[pos] - trend;
    DetailBuffer[pos] = detail;

    // تهيئة الإشارات
    BuySignalBuffer[pos] = EMPTY_VALUE;
    SellSignalBuffer[pos] = EMPTY_VALUE;

    // توليد الإشارات
    if(ShowSignals && pos < rates_total - WaveletPeriod - 1) {
        GenerateSignals(pos, trend, detail);
    }
}

//+------------------------------------------------------------------+
//| Generate Trading Signals                                         |
//+------------------------------------------------------------------+
void GenerateSignals(int pos, double trend, double detail)
{
    // التحقق من وجود بيانات سابقة
    if(TrendBuffer[pos + 1] == EMPTY_VALUE) return;

    double prevTrend = TrendBuffer[pos + 1];
    double prevDetail = DetailBuffer[pos + 1];

    double trendChange = trend - prevTrend;

    // شروط إشارة الشراء
    bool buyCondition = (trendChange > 0) &&
                       (detail > SignalThreshold) &&
                       (prevDetail <= 0);

    // شروط إشارة البيع
    bool sellCondition = (trendChange < 0) &&
                        (detail < -SignalThreshold) &&
                        (prevDetail >= 0);

    if(buyCondition) {
        BuySignalBuffer[pos] = detail;
    }

    if(sellCondition) {
        SellSignalBuffer[pos] = detail;
    }
}

//+------------------------------------------------------------------+
//| Deinitialization function                                        |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("Simple Wavelet Indicator deinitialized");
}
//+------------------------------------------------------------------+
