# مؤشرات Wavelet Transform للتحليل الفني

## نظرة عامة

هذه مجموعة من مؤشرات MQL4 المتقدمة التي تستخدم تقنية **Wavelet Transform** (تحويل الموجة) لتحليل البيانات الزمنية المالية واستخراج الأنماط الخفية والدورات الزمنية من أسعار الأسهم والعملات.

## المؤشرات المتوفرة

### 1. AdvancedWaveletAnalyzer.mq4
**المؤشر الأكثر تقدماً وشمولية**

#### الميزات:
- تحليل موجات متعدد المستويات (Multi-level Wavelet Decomposition)
- كشف الدورات الزمنية التلقائي
- استخراج الأنماط المخفية
- فترة متكيفة حسب التقلبات
- إشارات تداول قوية ومدروسة
- تنبيهات صوتية ومرئية

#### المخرجات:
- **الاتجاه الرئيسي** (أزرق): الاتجاه العام طويل المدى
- **التفاصيل عالية التردد** (أحمر): التقلبات قصيرة المدى
- **الدورة المتوسطة** (أخضر): الدورات متوسطة المدى
- **الدورة الطويلة** (برتقالي): الدورات طويلة المدى
- **إشارات الشراء** (أصفر): نقاط دخول قوية للشراء
- **إشارات البيع** (بنفسجي): نقاط دخول قوية للبيع
- **نقاط الانعكاس** (سماوي): نقاط انعكاس محتملة

#### الإعدادات الرئيسية:
```
WaveletPeriod = 32          // فترة تحليل الموجة الأساسية
DecompositionLevels = 4     // عدد مستويات التفكيك
NoiseThreshold = 0.1        // عتبة تصفية الضوضاء
SignalStrength = 0.7        // قوة الإشارة المطلوبة
UseAdaptivePeriod = true    // استخدام فترة متكيفة
```

### 2. WaveletCycleDetector.mq4
**مؤشر مبسط لكشف الدورات**

#### الميزات:
- تحليل مبسط وسهل الفهم
- كشف الدورات القصيرة والمتوسطة
- إشارات واضحة ومباشرة
- مناسب للمبتدئين

#### المخرجات:
- **الاتجاه الرئيسي** (أزرق): الاتجاه العام
- **الدورة القصيرة** (أحمر): دورات قصيرة المدى
- **الدورة المتوسطة** (أخضر): دورات متوسطة المدى
- **إشارات الشراء/البيع** (أصفر/بنفسجي)

### 3. WaveletSquareOf9.mq4
**مؤشر متقدم يجمع بين Wavelet وSquare of 9**

#### الميزات:
- دمج تحليل الموجات مع حسابات Square of 9
- مستويات دخول دقيقة
- انتظار إغلاق الشمعة قبل التنفيذ
- إدارة متقدمة للمستويات

### 4. SimpleWavelet.mq4
**مؤشر أساسي للبداية**

#### الميزات:
- تحليل موجات بسيط
- مناسب للتعلم والفهم
- إشارات أساسية

## المفاهيم الأساسية

### ما هو Wavelet Transform؟
تحويل الموجة هو تقنية رياضية متقدمة تسمح بـ:

1. **تحليل الترددات المختلفة**: فصل الحركات قصيرة المدى عن طويلة المدى
2. **كشف الأنماط المخفية**: استخراج أنماط غير مرئية في التحليل التقليدي
3. **تحديد الدورات الزمنية**: العثور على دورات متكررة في البيانات
4. **تصفية الضوضاء**: إزالة التقلبات العشوائية والتركيز على الإشارات الحقيقية

### كيف يعمل في التداول؟

#### 1. التفكيك متعدد المستويات:
- **المستوى 1**: الاتجاه العام (Trend)
- **المستوى 2**: الدورات المتوسطة (Medium Cycles)
- **المستوى 3**: التقلبات قصيرة المدى (Short-term Fluctuations)
- **المستوى 4**: الضوضاء والتفاصيل الدقيقة (Noise & Details)

#### 2. كشف الدورات:
- **دورات قصيرة** (8-16 شمعة): للتداول اليومي
- **دورات متوسطة** (20-50 شمعة): للتداول الأسبوعي
- **دورات طويلة** (50-100 شمعة): للاستثمار طويل المدى

#### 3. توليد الإشارات:
- **إشارة شراء**: عندما تتوافق عدة مستويات على الاتجاه الصاعد
- **إشارة بيع**: عندما تتوافق عدة مستويات على الاتجاه الهابط
- **نقاط الانعكاس**: عندما تتضارب المستويات المختلفة

## طريقة الاستخدام

### التثبيت:
1. انسخ ملفات .mq4 إلى مجلد `MQL4/Indicators`
2. أعد تشغيل MetaTrader 4
3. اسحب المؤشر إلى الرسم البياني

### الإعدادات المقترحة:

#### للتداول اليومي:
```
WaveletPeriod = 20
DecompositionLevels = 3
SignalStrength = 0.6
```

#### للتداول الأسبوعي:
```
WaveletPeriod = 50
DecompositionLevels = 4
SignalStrength = 0.7
```

#### للاستثمار طويل المدى:
```
WaveletPeriod = 100
DecompositionLevels = 5
SignalStrength = 0.8
```

### قراءة الإشارات:

#### إشارات الشراء القوية:
- الاتجاه الرئيسي صاعد
- الدورة المتوسطة إيجابية
- التفاصيل عالية التردد تدعم الصعود
- وجود نمط انعكاس صاعد

#### إشارات البيع القوية:
- الاتجاه الرئيسي هابط
- الدورة المتوسطة سلبية
- التفاصيل عالية التردد تدعم الهبوط
- وجود نمط انعكاس هابط

## نصائح للاستخدام الأمثل

### 1. الجمع مع مؤشرات أخرى:
- استخدم مع مؤشرات الحجم
- ادمج مع مستويات الدعم والمقاومة
- اجمع مع تحليل الشموع اليابانية

### 2. إدارة المخاطر:
- لا تعتمد على إشارة واحدة فقط
- استخدم وقف الخسارة دائماً
- تأكد من قوة الإشارة قبل الدخول

### 3. الأطر الزمنية:
- **H1**: للتداول اليومي السريع
- **H4**: للتداول متوسط المدى
- **D1**: للاستثمار طويل المدى

### 4. أزواج العملات المناسبة:
- **EUR/USD**: ممتاز للدورات المتوسطة
- **GBP/JPY**: جيد للتقلبات عالية التردد
- **USD/CHF**: مناسب للاتجاهات طويلة المدى

## الدعم الفني

### المشاكل الشائعة:
1. **عدم ظهور الإشارات**: تحقق من الإعدادات وقوة الإشارة
2. **إشارات كثيرة جداً**: ارفع قيمة SignalStrength
3. **إشارات قليلة جداً**: اخفض قيمة SignalStrength

### التحسين:
- اختبر الإعدادات على البيانات التاريخية
- اضبط الفترات حسب تقلبات السوق
- استخدم الفترة المتكيفة للأسواق المتغيرة

---

**ملاحظة**: هذه المؤشرات أدوات تحليل متقدمة تتطلب فهماً جيداً لتحليل الموجات والدورات الزمنية. يُنصح بالتدريب على حساب تجريبي قبل الاستخدام الفعلي.
