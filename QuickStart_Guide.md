# دليل البدء السريع - مؤشرات Wavelet Transform

## التثبيت السريع

### الخطوة 1: نسخ الملفات
```
انسخ جميع ملفات .mq4 إلى مجلد:
MetaTrader 4/MQL4/Indicators/

انسخ ملف .mqh إلى مجلد:
MetaTrader 4/MQL4/Include/
```

### الخطوة 2: إعادة التشغيل
- أعد تشغيل MetaTrader 4
- ستجد المؤشرات في قائمة Navigator > Indicators

## الاستخدام السريع

### للمبتدئين - ابدأ بـ WaveletCycleDetector

#### الإعدادات المقترحة:
```
MainPeriod = 20
ShortCyclePeriod = 8
MediumCyclePeriod = 16
SignalThreshold = 0.3
ShowSignals = true
```

#### كيفية القراءة:
- **الخط الأزرق**: الاتجاه العام
- **الخط الأحمر**: الدورة القصيرة
- **الخط الأخضر**: الدورة المتوسطة
- **السهم الأصفر**: إشارة شراء
- **السهم البنفسجي**: إشارة بيع

### للمتقدمين - استخدم AdvancedWaveletAnalyzer

#### الإعدادات المقترحة:
```
WaveletPeriod = 32
DecompositionLevels = 4
SignalStrength = 0.7
UseAdaptivePeriod = true
DetectCycles = true
ShowPatterns = true
```

#### المخرجات:
- **الاتجاه الرئيسي** (أزرق): الاتجاه طويل المدى
- **التفاصيل عالية التردد** (أحمر): التقلبات السريعة
- **الدورة المتوسطة** (أخضر): الدورات متوسطة المدى
- **الدورة الطويلة** (برتقالي): الدورات طويلة المدى
- **نقاط الانعكاس** (سماوي): نقاط تغيير الاتجاه

## الإعدادات حسب نوع التداول

### التداول السريع (Scalping)
```
الإطار الزمني: M1, M5
المؤشر: WaveletCycleDetector
الإعدادات:
- MainPeriod = 16
- SignalThreshold = 0.3
- ShowSignals = true
```

### التداول اليومي
```
الإطار الزمني: M15, M30, H1
المؤشر: AdvancedWaveletAnalyzer
الإعدادات:
- WaveletPeriod = 32
- DecompositionLevels = 3
- SignalStrength = 0.6
```

### التداول المتوسط
```
الإطار الزمني: H4, D1
المؤشر: AdvancedWaveletAnalyzer
الإعدادات:
- WaveletPeriod = 64
- DecompositionLevels = 4
- SignalStrength = 0.7
```

## الإعدادات حسب زوج العملة

### العملات الرئيسية (EUR/USD, GBP/USD)
```
SignalThreshold = 0.5
NoiseThreshold = 0.12
MinCyclePeriod = 12
MaxCyclePeriod = 60
```

### العملات الثانوية (AUD/USD, USD/CAD)
```
SignalThreshold = 0.55
NoiseThreshold = 0.18
MinCyclePeriod = 10
MaxCyclePeriod = 45
```

### العملات الغريبة (USD/TRY, GBP/JPY)
```
SignalThreshold = 0.75
NoiseThreshold = 0.25
UseAdaptivePeriod = true
```

## قراءة الإشارات

### إشارة شراء قوية ✅
- الاتجاه الرئيسي صاعد (أزرق يرتفع)
- الدورة المتوسطة إيجابية (أخضر فوق الصفر)
- التفاصيل عالية التردد تدعم الصعود
- ظهور سهم أصفر

### إشارة بيع قوية ❌
- الاتجاه الرئيسي هابط (أزرق ينخفض)
- الدورة المتوسطة سلبية (أخضر تحت الصفر)
- التفاصيل عالية التردد تدعم الهبوط
- ظهور سهم بنفسجي

### تجنب التداول ⚠️
- الخطوط متضاربة
- الإشارات ضعيفة
- التقلبات عالية جداً
- عدم وجود اتجاه واضح

## نصائح سريعة

### ✅ افعل:
- استخدم وقف الخسارة دائماً
- انتظر تأكيد الإشارة
- اجمع مع مؤشرات أخرى
- اختبر على حساب تجريبي أولاً

### ❌ لا تفعل:
- لا تتداول على إشارة واحدة فقط
- لا تتجاهل إدارة المخاطر
- لا تستخدم رافعة مالية عالية
- لا تتداول أثناء الأخبار المهمة

## استكشاف الأخطاء

### المشكلة: لا تظهر إشارات
**الحل:**
- اخفض قيمة SignalStrength
- تأكد من تفعيل ShowSignals
- زد فترة التحليل

### المشكلة: إشارات كثيرة جداً
**الحل:**
- ارفع قيمة SignalThreshold
- استخدم فترة أطول
- فعّل UseAdaptivePeriod

### المشكلة: إشارات خاطئة
**الحل:**
- ارفع قيمة SignalStrength
- استخدم إطار زمني أكبر
- اجمع مع مؤشرات تأكيد

## الاختبار والتحسين

### استخدم WaveletTestScript.mq4:
```
1. اسحب السكريبت على الرسم البياني
2. اضبط فترة الاختبار
3. فعّل المؤشرات المراد اختبارها
4. شاهد النتائج في الخبراء
```

### مؤشرات الأداء:
- **الدقة**: نسبة الإشارات الصحيحة
- **القوة**: متوسط قوة الإشارات
- **التكرار**: عدد الإشارات في الفترة

## الدعم والمساعدة

### الملفات المرجعية:
- `README_Wavelet_Indicators.md`: الدليل الشامل
- `WaveletSettings.mqh`: الإعدادات المحسنة
- `WaveletTestScript.mq4`: سكريبت الاختبار

### المؤشرات المتوفرة:
1. **SimpleWavelet.mq4**: للمبتدئين
2. **WaveletCycleDetector.mq4**: كشف الدورات
3. **AdvancedWaveletAnalyzer.mq4**: التحليل المتقدم
4. **WaveletSpectralAnalysis.mq4**: التحليل الطيفي
5. **WaveletSquareOf9.mq4**: مع Square of 9

### نصائح الأداء:
- استخدم VPS للتداول الآلي
- راقب استهلاك الذاكرة
- احفظ الإعدادات المفضلة
- اعمل نسخ احتياطية منتظمة

---

## مثال عملي سريع

### السيناريو: تداول EUR/USD على H1

#### الإعداد:
```
1. افتح EUR/USD على إطار H1
2. اسحب AdvancedWaveletAnalyzer
3. استخدم الإعدادات:
   - WaveletPeriod = 32
   - SignalStrength = 0.6
   - EnableSignals = true
```

#### المراقبة:
```
- راقب الخط الأزرق للاتجاه العام
- انتظر ظهور سهم أصفر (شراء) أو بنفسجي (بيع)
- تأكد من أن الدورات تدعم الإشارة
- ضع وقف الخسارة 20-30 نقطة
```

#### الدخول:
```
- ادخل عند ظهور الإشارة
- استهدف 40-60 نقطة ربح
- اخرج إذا انعكست الإشارة
```

---

**تذكر**: هذه أدوات تحليل متقدمة تتطلب ممارسة وفهم. ابدأ بالمؤشرات البسيطة ثم انتقل للمتقدمة تدريجياً.
