//+------------------------------------------------------------------+
//|                                      AdvancedWaveletAnalyzer.mq4 |
//|                    مؤشر تحليل الموجات المتقدم - Wavelet Transform |
//|                          لاستخراج الأنماط الخفية والدورات الزمنية |
//+------------------------------------------------------------------+
#property copyright "Advanced Wavelet Analyzer"
#property link      ""
#property version   "2.00"
#property strict

// إعدادات المؤشر
#property indicator_separate_window
#property indicator_buffers 8
#property indicator_plots   8

// ألوان المؤشر
#property indicator_color1  clrBlue      // الاتجاه الرئيسي
#property indicator_color2  clrRed       // التفاصيل عالية التردد
#property indicator_color3  clrGreen     // الدورة المتوسطة
#property indicator_color4  clrOrange    // الدورة الطويلة
#property indicator_color5  clrYellow    // إشارة شراء قوية
#property indicator_color6  clrMagenta   // إشارة بيع قوية
#property indicator_color7  clrCyan      // نقاط الانعكاس
#property indicator_color8  clrGray      // خط الصفر

// أنماط الخطوط
#property indicator_style1  STYLE_SOLID
#property indicator_style2  STYLE_SOLID
#property indicator_style3  STYLE_DASH
#property indicator_style4  STYLE_DASH
#property indicator_style5  STYLE_SOLID
#property indicator_style6  STYLE_SOLID
#property indicator_style7  STYLE_DOT
#property indicator_style8  STYLE_DOT

// عرض الخطوط
#property indicator_width1  2
#property indicator_width2  1
#property indicator_width3  1
#property indicator_width4  1
#property indicator_width5  3
#property indicator_width6  3
#property indicator_width7  2
#property indicator_width8  1

//+------------------------------------------------------------------+
//| المدخلات الخارجية                                               |
//+------------------------------------------------------------------+
extern int WaveletPeriod = 32;           // فترة تحليل الموجة الأساسية
extern int DecompositionLevels = 4;      // عدد مستويات التفكيك
extern double NoiseThreshold = 0.1;      // عتبة تصفية الضوضاء
extern bool DetectCycles = true;         // كشف الدورات الزمنية
extern bool ShowPatterns = true;         // إظهار الأنماط المخفية
extern bool EnableSignals = true;        // تفعيل إشارات التداول
extern double SignalStrength = 0.7;      // قوة الإشارة المطلوبة
extern bool UseAdaptivePeriod = true;    // استخدام فترة متكيفة
extern int MaxCyclePeriod = 100;         // أقصى فترة دورة
extern int MinCyclePeriod = 8;           // أقل فترة دورة
extern bool EnableAlerts = false;        // تفعيل التنبيهات
extern string AlertSound = "alert2.wav"; // ملف الصوت

//+------------------------------------------------------------------+
//| المتغيرات العامة                                                |
//+------------------------------------------------------------------+
// مصفوفات البيانات
double MainTrendBuffer[];        // الاتجاه الرئيسي
double HighFreqBuffer[];         // التفاصيل عالية التردد
double MidCycleBuffer[];         // الدورة المتوسطة
double LongCycleBuffer[];        // الدورة الطويلة
double BuySignalBuffer[];        // إشارات الشراء القوية
double SellSignalBuffer[];       // إشارات البيع القوية
double ReversalBuffer[];         // نقاط الانعكاس
double ZeroLineBuffer[];         // خط الصفر

// مصفوفات العمل
double WorkingData[];
double TempData[];
double CoefficientA[];
double CoefficientD[];
double CycleData[];
double PatternData[];

// متغيرات الدورات والأنماط
struct CycleInfo {
    int period;
    double amplitude;
    double phase;
    double strength;
    bool isActive;
};
CycleInfo DetectedCycles[10];
int CycleCount = 0;

struct PatternInfo {
    int startBar;
    int endBar;
    double strength;
    int type; // 1=صاعد, -1=هابط, 0=عرضي
    bool isConfirmed;
};
PatternInfo DetectedPatterns[20];
int PatternCount = 0;

datetime LastAlertTime = 0;
double LastPrice = 0;
int AdaptivePeriod = 32;

//+------------------------------------------------------------------+
//| دالة التهيئة                                                    |
//+------------------------------------------------------------------+
int OnInit()
{
    // ربط المصفوفات
    SetIndexBuffer(0, MainTrendBuffer);
    SetIndexBuffer(1, HighFreqBuffer);
    SetIndexBuffer(2, MidCycleBuffer);
    SetIndexBuffer(3, LongCycleBuffer);
    SetIndexBuffer(4, BuySignalBuffer);
    SetIndexBuffer(5, SellSignalBuffer);
    SetIndexBuffer(6, ReversalBuffer);
    SetIndexBuffer(7, ZeroLineBuffer);

    // تسميات المؤشرات
    SetIndexLabel(0, "الاتجاه الرئيسي");
    SetIndexLabel(1, "التفاصيل عالية التردد");
    SetIndexLabel(2, "الدورة المتوسطة");
    SetIndexLabel(3, "الدورة الطويلة");
    SetIndexLabel(4, "إشارة شراء قوية");
    SetIndexLabel(5, "إشارة بيع قوية");
    SetIndexLabel(6, "نقاط الانعكاس");
    SetIndexLabel(7, "خط الصفر");

    // أنماط الرسم
    SetIndexStyle(0, DRAW_LINE, STYLE_SOLID, 2, clrBlue);
    SetIndexStyle(1, DRAW_LINE, STYLE_SOLID, 1, clrRed);
    SetIndexStyle(2, DRAW_LINE, STYLE_DASH, 1, clrGreen);
    SetIndexStyle(3, DRAW_LINE, STYLE_DASH, 1, clrOrange);
    SetIndexStyle(4, DRAW_ARROW, STYLE_SOLID, 3, clrYellow);
    SetIndexStyle(5, DRAW_ARROW, STYLE_SOLID, 3, clrMagenta);
    SetIndexStyle(6, DRAW_ARROW, STYLE_SOLID, 2, clrCyan);
    SetIndexStyle(7, DRAW_LINE, STYLE_DOT, 1, clrGray);

    // رموز الأسهم
    SetIndexArrow(4, 233); // سهم شراء
    SetIndexArrow(5, 234); // سهم بيع
    SetIndexArrow(6, 159); // نقطة انعكاس

    // إخفاء المؤشرات حسب الإعدادات
    if(!EnableSignals) {
        SetIndexStyle(4, DRAW_NONE);
        SetIndexStyle(5, DRAW_NONE);
    }
    if(!ShowPatterns) {
        SetIndexStyle(6, DRAW_NONE);
    }

    // تهيئة المصفوفات
    int maxPeriod = MathMax(WaveletPeriod, MaxCyclePeriod);
    ArrayResize(WorkingData, maxPeriod * 2);
    ArrayResize(TempData, maxPeriod * 2);
    ArrayResize(CoefficientA, maxPeriod);
    ArrayResize(CoefficientD, maxPeriod);
    ArrayResize(CycleData, maxPeriod);
    ArrayResize(PatternData, maxPeriod);

    // تهيئة الدورات والأنماط
    InitializeCycleDetection();
    InitializePatternDetection();

    IndicatorShortName("Advanced Wavelet (" + IntegerToString(WaveletPeriod) + "," + 
                      IntegerToString(DecompositionLevels) + ")");
    
    Print("تم تهيئة مؤشر Wavelet Transform المتقدم بنجاح");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| دالة الحساب الرئيسية                                            |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    if(rates_total < WaveletPeriod) return 0;
    
    // تحديد نقطة البداية
    int start = MathMax(prev_calculated - 1, WaveletPeriod - 1);
    if(start < WaveletPeriod - 1) start = WaveletPeriod - 1;
    
    // تحديث الفترة المتكيفة
    if(UseAdaptivePeriod) {
        UpdateAdaptivePeriod(close, rates_total);
    }
    
    // الحساب للشموع المغلقة
    for(int i = start; i >= 1; i--) {
        CalculateAdvancedWavelet(i, close, high, low, rates_total);
    }
    
    return rates_total;
}

//+------------------------------------------------------------------+
//| حساب تحليل الموجات المتقدم                                      |
//+------------------------------------------------------------------+
void CalculateAdvancedWavelet(int pos, const double &close[], const double &high[], 
                             const double &low[], int total)
{
    if(pos == 0) return; // تجاهل الشمعة الحالية
    
    // تحضير البيانات
    PrepareWaveletData(pos, close, total);
    
    // تطبيق تحليل الموجات متعدد المستويات
    ApplyMultiLevelWaveletDecomposition();
    
    // استخراج المكونات المختلفة
    ExtractWaveletComponents(pos);
    
    // كشف الدورات الزمنية
    if(DetectCycles) {
        DetectTimeCycles(pos, close);
    }
    
    // كشف الأنماط المخفية
    if(ShowPatterns) {
        DetectHiddenPatterns(pos, close);
    }
    
    // توليد الإشارات
    if(EnableSignals) {
        GenerateAdvancedSignals(pos, close, high, low);
    }
    
    // رسم خط الصفر
    ZeroLineBuffer[pos] = 0.0;
}

//+------------------------------------------------------------------+
//| تحضير بيانات الموجة                                             |
//+------------------------------------------------------------------+
void PrepareWaveletData(int pos, const double &close[], int total)
{
    int period = UseAdaptivePeriod ? AdaptivePeriod : WaveletPeriod;
    
    // نسخ البيانات مع التحقق من الحدود
    for(int i = 0; i < period; i++) {
        int index = pos - i;
        if(index >= 0 && index < total) {
            WorkingData[i] = close[index];
        } else if(index < 0) {
            WorkingData[i] = close[0]; // استخدام أول قيمة متاحة
        } else {
            WorkingData[i] = close[total-1]; // استخدام آخر قيمة متاحة
        }
    }
    
    // تطبيق تصفية الضوضاء
    ApplyNoiseFilter(period);
}

//+------------------------------------------------------------------+
//| تطبيق تحليل الموجات متعدد المستويات                             |
//+------------------------------------------------------------------+
void ApplyMultiLevelWaveletDecomposition()
{
    int period = UseAdaptivePeriod ? AdaptivePeriod : WaveletPeriod;
    ArrayCopy(TempData, WorkingData, 0, 0, period);

    // تطبيق تفكيك الموجة على عدة مستويات
    for(int level = 0; level < DecompositionLevels; level++) {
        ApplyDaubechiesWavelet(TempData, period, level);
    }
}

//+------------------------------------------------------------------+
//| تطبيق موجة Daubechies                                           |
//+------------------------------------------------------------------+
void ApplyDaubechiesWavelet(double &data[], int size, int level)
{
    if(size < 4) return;

    // معاملات موجة Daubechies-4
    double h0 = 0.6830127, h1 = 1.1830127, h2 = 0.3169873, h3 = -0.1830127;
    double g0 = -h3, g1 = h2, g2 = -h1, g3 = h0;

    int halfSize = size / 2;
    ArrayResize(CoefficientA, halfSize);
    ArrayResize(CoefficientD, halfSize);

    // حساب معاملات التقريب والتفاصيل
    for(int i = 0; i < halfSize; i++) {
        int idx = 2 * i;
        if(idx + 3 < size) {
            CoefficientA[i] = h0*data[idx] + h1*data[idx+1] + h2*data[idx+2] + h3*data[idx+3];
            CoefficientD[i] = g0*data[idx] + g1*data[idx+1] + g2*data[idx+2] + g3*data[idx+3];
        }
    }

    // إعادة بناء البيانات مع التفاصيل المفلترة
    for(int i = 0; i < halfSize && i < size; i++) {
        data[i] = CoefficientA[i];
        if(i + halfSize < size) {
            data[i + halfSize] = MathAbs(CoefficientD[i]) > NoiseThreshold ? CoefficientD[i] : 0;
        }
    }
}

//+------------------------------------------------------------------+
//| تطبيق مرشح الضوضاء                                              |
//+------------------------------------------------------------------+
void ApplyNoiseFilter(int period)
{
    // مرشح متوسط متحرك بسيط لتقليل الضوضاء
    for(int i = 1; i < period - 1; i++) {
        double avg = (WorkingData[i-1] + WorkingData[i] + WorkingData[i+1]) / 3.0;
        if(MathAbs(WorkingData[i] - avg) < NoiseThreshold) {
            WorkingData[i] = avg;
        }
    }
}

//+------------------------------------------------------------------+
//| استخراج مكونات الموجة                                           |
//+------------------------------------------------------------------+
void ExtractWaveletComponents(int pos)
{
    int period = UseAdaptivePeriod ? AdaptivePeriod : WaveletPeriod;

    // الاتجاه الرئيسي (المكون منخفض التردد)
    double mainTrend = 0;
    int trendCount = 0;
    for(int i = 0; i < period/4; i++) {
        mainTrend += TempData[i];
        trendCount++;
    }
    MainTrendBuffer[pos] = trendCount > 0 ? mainTrend / trendCount : 0;

    // التفاصيل عالية التردد
    double highFreq = 0;
    int freqCount = 0;
    for(int i = period/2; i < 3*period/4; i++) {
        if(i < ArraySize(TempData)) {
            highFreq += TempData[i];
            freqCount++;
        }
    }
    HighFreqBuffer[pos] = freqCount > 0 ? highFreq / freqCount : 0;

    // الدورة المتوسطة
    MidCycleBuffer[pos] = CalculateMidCycleComponent(pos);

    // الدورة الطويلة
    LongCycleBuffer[pos] = CalculateLongCycleComponent(pos);
}

//+------------------------------------------------------------------+
//| حساب مكون الدورة المتوسطة                                       |
//+------------------------------------------------------------------+
double CalculateMidCycleComponent(int pos)
{
    int midPeriod = (MaxCyclePeriod + MinCyclePeriod) / 2;
    double sum = 0;
    int count = 0;

    for(int i = 0; i < midPeriod && i < ArraySize(TempData); i++) {
        sum += TempData[i] * MathCos(2 * M_PI * i / midPeriod);
        count++;
    }

    return count > 0 ? sum / count : 0;
}

//+------------------------------------------------------------------+
//| حساب مكون الدورة الطويلة                                        |
//+------------------------------------------------------------------+
double CalculateLongCycleComponent(int pos)
{
    double sum = 0;
    int count = 0;

    for(int i = 0; i < MaxCyclePeriod && i < ArraySize(TempData); i++) {
        sum += TempData[i] * MathCos(2 * M_PI * i / MaxCyclePeriod);
        count++;
    }

    return count > 0 ? sum / count : 0;
}

//+------------------------------------------------------------------+
//| تحديث الفترة المتكيفة                                           |
//+------------------------------------------------------------------+
void UpdateAdaptivePeriod(const double &close[], int total)
{
    // حساب التقلبات لتحديد الفترة المناسبة
    double volatility = CalculateVolatility(close, total, 20);

    // تكييف الفترة حسب التقلبات
    if(volatility > 0.02) {
        AdaptivePeriod = MathMax(MinCyclePeriod, WaveletPeriod / 2);
    } else if(volatility < 0.005) {
        AdaptivePeriod = MathMin(MaxCyclePeriod, WaveletPeriod * 2);
    } else {
        AdaptivePeriod = WaveletPeriod;
    }
}

//+------------------------------------------------------------------+
//| حساب التقلبات                                                   |
//+------------------------------------------------------------------+
double CalculateVolatility(const double &close[], int total, int period)
{
    if(total < period + 1) return 0;

    double sum = 0;
    for(int i = 1; i <= period; i++) {
        if(close[i] > 0 && close[i-1] > 0) {
            double change = MathLog(close[i-1] / close[i]);
            sum += change * change;
        }
    }

    return MathSqrt(sum / period);
}

//+------------------------------------------------------------------+
//| تهيئة كشف الدورات                                               |
//+------------------------------------------------------------------+
void InitializeCycleDetection()
{
    CycleCount = 0;
    for(int i = 0; i < 10; i++) {
        DetectedCycles[i].period = 0;
        DetectedCycles[i].amplitude = 0;
        DetectedCycles[i].phase = 0;
        DetectedCycles[i].strength = 0;
        DetectedCycles[i].isActive = false;
    }
}

//+------------------------------------------------------------------+
//| تهيئة كشف الأنماط                                               |
//+------------------------------------------------------------------+
void InitializePatternDetection()
{
    PatternCount = 0;
    for(int i = 0; i < 20; i++) {
        DetectedPatterns[i].startBar = 0;
        DetectedPatterns[i].endBar = 0;
        DetectedPatterns[i].strength = 0;
        DetectedPatterns[i].type = 0;
        DetectedPatterns[i].isConfirmed = false;
    }
}

//+------------------------------------------------------------------+
//| كشف الدورات الزمنية                                             |
//+------------------------------------------------------------------+
void DetectTimeCycles(int pos, const double &close[])
{
    // البحث عن دورات في نطاقات مختلفة
    for(int period = MinCyclePeriod; period <= MaxCyclePeriod; period += 4) {
        double cycleStrength = CalculateCycleStrength(pos, close, period);

        if(cycleStrength > 0.6) { // عتبة قوة الدورة
            UpdateCycleInfo(period, cycleStrength, pos);
        }
    }
}

//+------------------------------------------------------------------+
//| حساب قوة الدورة                                                 |
//+------------------------------------------------------------------+
double CalculateCycleStrength(int pos, const double &close[], int period)
{
    if(pos < period * 2) return 0;

    double correlation = 0;
    int validPoints = 0;

    // حساب الارتباط الذاتي للدورة
    for(int i = 0; i < period; i++) {
        if(pos - i >= 0 && pos - i - period >= 0) {
            double current = close[pos - i];
            double previous = close[pos - i - period];
            correlation += current * previous;
            validPoints++;
        }
    }

    if(validPoints == 0) return 0;

    // تطبيع النتيجة
    double avgCurrent = 0, avgPrevious = 0;
    for(int i = 0; i < period; i++) {
        if(pos - i >= 0 && pos - i - period >= 0) {
            avgCurrent += close[pos - i];
            avgPrevious += close[pos - i - period];
        }
    }
    avgCurrent /= validPoints;
    avgPrevious /= validPoints;

    return MathAbs(correlation / validPoints - avgCurrent * avgPrevious);
}

//+------------------------------------------------------------------+
//| تحديث معلومات الدورة                                            |
//+------------------------------------------------------------------+
void UpdateCycleInfo(int period, double strength, int pos)
{
    // البحث عن دورة موجودة أو إضافة جديدة
    int cycleIndex = -1;
    for(int i = 0; i < CycleCount; i++) {
        if(MathAbs(DetectedCycles[i].period - period) <= 2) {
            cycleIndex = i;
            break;
        }
    }

    if(cycleIndex == -1 && CycleCount < 10) {
        cycleIndex = CycleCount;
        CycleCount++;
    }

    if(cycleIndex >= 0) {
        DetectedCycles[cycleIndex].period = period;
        DetectedCycles[cycleIndex].strength = strength;
        DetectedCycles[cycleIndex].isActive = true;
        DetectedCycles[cycleIndex].phase = CalculatePhase(pos, period);
        DetectedCycles[cycleIndex].amplitude = CalculateAmplitude(pos, period);
    }
}

//+------------------------------------------------------------------+
//| حساب الطور                                                      |
//+------------------------------------------------------------------+
double CalculatePhase(int pos, int period)
{
    // حساب الطور باستخدام تحويل هيلبرت المبسط
    double real = 0, imag = 0;
    int count = 0;

    for(int i = 0; i < period/2; i++) {
        if(pos - i >= 0) {
            double angle = 2 * M_PI * i / period;
            real += Close[pos - i] * MathCos(angle);
            imag += Close[pos - i] * MathSin(angle);
            count++;
        }
    }

    if(count == 0) return 0;
    return MathArctan2(imag, real);
}

//+------------------------------------------------------------------+
//| حساب السعة                                                      |
//+------------------------------------------------------------------+
double CalculateAmplitude(int pos, int period)
{
    double maxVal = -999999, minVal = 999999;

    for(int i = 0; i < period; i++) {
        if(pos - i >= 0) {
            maxVal = MathMax(maxVal, Close[pos - i]);
            minVal = MathMin(minVal, Close[pos - i]);
        }
    }

    return (maxVal - minVal) / 2.0;
}

//+------------------------------------------------------------------+
//| كشف الأنماط المخفية                                             |
//+------------------------------------------------------------------+
void DetectHiddenPatterns(int pos, const double &close[])
{
    // كشف أنماط الانعكاس
    DetectReversalPatterns(pos, close);

    // كشف أنماط الاستمرار
    DetectContinuationPatterns(pos, close);

    // تحديث نقاط الانعكاس
    UpdateReversalPoints(pos);
}

//+------------------------------------------------------------------+
//| كشف أنماط الانعكاس                                              |
//+------------------------------------------------------------------+
void DetectReversalPatterns(int pos, const double &close[])
{
    if(pos < 10) return;

    // نمط الانعكاس الصاعد
    bool bullishReversal = (MainTrendBuffer[pos] > MainTrendBuffer[pos-1]) &&
                          (MainTrendBuffer[pos-1] < MainTrendBuffer[pos-2]) &&
                          (HighFreqBuffer[pos] > 0) &&
                          (MidCycleBuffer[pos] > MidCycleBuffer[pos-1]);

    // نمط الانعكاس الهابط
    bool bearishReversal = (MainTrendBuffer[pos] < MainTrendBuffer[pos-1]) &&
                          (MainTrendBuffer[pos-1] > MainTrendBuffer[pos-2]) &&
                          (HighFreqBuffer[pos] < 0) &&
                          (MidCycleBuffer[pos] < MidCycleBuffer[pos-1]);

    if(bullishReversal || bearishReversal) {
        AddPattern(pos-2, pos, bullishReversal ? 1 : -1, 0.8);
    }
}

//+------------------------------------------------------------------+
//| كشف أنماط الاستمرار                                             |
//+------------------------------------------------------------------+
void DetectContinuationPatterns(int pos, const double &close[])
{
    if(pos < 15) return;

    // نمط الاستمرار الصاعد
    bool bullishContinuation = true;
    for(int i = 1; i <= 5; i++) {
        if(MainTrendBuffer[pos-i] <= MainTrendBuffer[pos-i-1]) {
            bullishContinuation = false;
            break;
        }
    }

    // نمط الاستمرار الهابط
    bool bearishContinuation = true;
    for(int i = 1; i <= 5; i++) {
        if(MainTrendBuffer[pos-i] >= MainTrendBuffer[pos-i-1]) {
            bearishContinuation = false;
            break;
        }
    }

    if(bullishContinuation || bearishContinuation) {
        AddPattern(pos-5, pos, bullishContinuation ? 1 : -1, 0.6);
    }
}

//+------------------------------------------------------------------+
//| إضافة نمط جديد                                                  |
//+------------------------------------------------------------------+
void AddPattern(int startBar, int endBar, int type, double strength)
{
    if(PatternCount >= 20) return;

    DetectedPatterns[PatternCount].startBar = startBar;
    DetectedPatterns[PatternCount].endBar = endBar;
    DetectedPatterns[PatternCount].type = type;
    DetectedPatterns[PatternCount].strength = strength;
    DetectedPatterns[PatternCount].isConfirmed = true;
    PatternCount++;
}

//+------------------------------------------------------------------+
//| تحديث نقاط الانعكاس                                             |
//+------------------------------------------------------------------+
void UpdateReversalPoints(int pos)
{
    ReversalBuffer[pos] = EMPTY_VALUE;

    // البحث عن نقاط انعكاس قوية
    for(int i = 0; i < PatternCount; i++) {
        if(DetectedPatterns[i].isConfirmed &&
           pos >= DetectedPatterns[i].startBar &&
           pos <= DetectedPatterns[i].endBar &&
           DetectedPatterns[i].strength > 0.7) {
            ReversalBuffer[pos] = MainTrendBuffer[pos];
            break;
        }
    }
}

//+------------------------------------------------------------------+
//| توليد الإشارات المتقدمة                                          |
//+------------------------------------------------------------------+
void GenerateAdvancedSignals(int pos, const double &close[], const double &high[], const double &low[])
{
    BuySignalBuffer[pos] = EMPTY_VALUE;
    SellSignalBuffer[pos] = EMPTY_VALUE;

    if(pos < 5) return;

    // حساب قوة الإشارة
    double signalStrength = CalculateSignalStrength(pos);

    if(signalStrength < SignalStrength) return;

    // شروط إشارة الشراء القوية
    bool strongBuySignal = (MainTrendBuffer[pos] > MainTrendBuffer[pos-1]) &&
                          (HighFreqBuffer[pos] > 0) &&
                          (MidCycleBuffer[pos] > 0) &&
                          (LongCycleBuffer[pos] > LongCycleBuffer[pos-1]) &&
                          IsActiveCycleSupporting(true) &&
                          IsPatternsSupporting(pos, true);

    // شروط إشارة البيع القوية
    bool strongSellSignal = (MainTrendBuffer[pos] < MainTrendBuffer[pos-1]) &&
                           (HighFreqBuffer[pos] < 0) &&
                           (MidCycleBuffer[pos] < 0) &&
                           (LongCycleBuffer[pos] < LongCycleBuffer[pos-1]) &&
                           IsActiveCycleSupporting(false) &&
                           IsPatternsSupporting(pos, false);

    if(strongBuySignal) {
        BuySignalBuffer[pos] = low[pos] - 10 * Point;
        if(EnableAlerts) {
            SendAdvancedAlert("إشارة شراء قوية - Wavelet", close[pos], signalStrength);
        }
    }

    if(strongSellSignal) {
        SellSignalBuffer[pos] = high[pos] + 10 * Point;
        if(EnableAlerts) {
            SendAdvancedAlert("إشارة بيع قوية - Wavelet", close[pos], signalStrength);
        }
    }
}

//+------------------------------------------------------------------+
//| حساب قوة الإشارة                                                |
//+------------------------------------------------------------------+
double CalculateSignalStrength(int pos)
{
    double strength = 0;

    // قوة الاتجاه الرئيسي
    if(pos >= 1) {
        strength += MathAbs(MainTrendBuffer[pos] - MainTrendBuffer[pos-1]) * 0.4;
    }

    // قوة التفاصيل
    strength += MathAbs(HighFreqBuffer[pos]) * 0.2;

    // قوة الدورات
    strength += MathAbs(MidCycleBuffer[pos]) * 0.2;
    strength += MathAbs(LongCycleBuffer[pos]) * 0.2;

    return MathMin(strength, 1.0);
}

//+------------------------------------------------------------------+
//| فحص دعم الدورات النشطة                                          |
//+------------------------------------------------------------------+
bool IsActiveCycleSupporting(bool isBullish)
{
    int supportingCycles = 0;
    int totalActiveCycles = 0;

    for(int i = 0; i < CycleCount; i++) {
        if(DetectedCycles[i].isActive && DetectedCycles[i].strength > 0.5) {
            totalActiveCycles++;

            // فحص اتجاه الدورة
            double phaseDirection = MathSin(DetectedCycles[i].phase);
            if((isBullish && phaseDirection > 0) || (!isBullish && phaseDirection < 0)) {
                supportingCycles++;
            }
        }
    }

    return totalActiveCycles == 0 || (supportingCycles * 2 > totalActiveCycles);
}

//+------------------------------------------------------------------+
//| فحص دعم الأنماط                                                 |
//+------------------------------------------------------------------+
bool IsPatternsSupporting(int pos, bool isBullish)
{
    for(int i = 0; i < PatternCount; i++) {
        if(DetectedPatterns[i].isConfirmed &&
           pos >= DetectedPatterns[i].startBar &&
           pos <= DetectedPatterns[i].endBar) {

            if((isBullish && DetectedPatterns[i].type > 0) ||
               (!isBullish && DetectedPatterns[i].type < 0)) {
                return true;
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| إرسال تنبيه متقدم                                               |
//+------------------------------------------------------------------+
void SendAdvancedAlert(string message, double price, double strength)
{
    datetime currentTime = TimeCurrent();
    if(currentTime - LastAlertTime < 300) return; // تنبيه كل 5 دقائق كحد أقصى

    string fullMessage = message + " على " + Symbol() +
                        " السعر: " + DoubleToString(price, Digits) +
                        " القوة: " + DoubleToString(strength * 100, 1) + "%";

    Alert(fullMessage);
    if(AlertSound != "") {
        PlaySound(AlertSound);
    }

    LastAlertTime = currentTime;
    Print("Wavelet Alert: ", fullMessage);
}

//+------------------------------------------------------------------+
//| دالة إنهاء التهيئة                                              |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("تم إنهاء مؤشر Wavelet Transform المتقدم");
    Comment("");
}

//+------------------------------------------------------------------+
